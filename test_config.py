#!/usr/bin/env python3
"""
测试配置文件功能
验证config.yaml是否正确加载和使用
"""

import os
import sys
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from cribnet_to_text_converter import CribNetTextConverter, load_config

def test_config_loading():
    """测试配置文件加载"""
    print("=== 测试配置文件加载 ===\n")
    
    config_file = "config.yaml"
    
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        return False
    
    print(f"加载配置文件: {config_file}")
    config = load_config(config_file)
    
    if not config:
        print("❌ 配置文件加载失败")
        return False
    
    print("✓ 配置文件加载成功")
    
    # 显示关键配置
    print("\n关键配置信息:")
    
    dataset_config = config.get('dataset', {})
    print(f"  数据集路径: {dataset_config.get('source_path', 'N/A')}")
    print(f"  输出路径: {dataset_config.get('output_path', 'N/A')}")
    print(f"  处理子集: {dataset_config.get('subset', 'N/A')}")
    print(f"  最大图像数: {dataset_config.get('max_images', 'N/A')}")
    
    model_config = config.get('model', {})
    print(f"  模型类型: {model_config.get('type', 'N/A')}")
    print(f"  API密钥: {'已设置' if model_config.get('api_key') else '未设置'}")
    
    qwen_config = model_config.get('qwen', {})
    if qwen_config:
        print(f"  Qwen模型: {qwen_config.get('model_name', 'N/A')}")
        print(f"  最大tokens: {qwen_config.get('max_tokens', 'N/A')}")
        print(f"  温度: {qwen_config.get('temperature', 'N/A')}")
    
    return True

def test_converter_with_config():
    """测试使用配置创建转换器"""
    print("\n=== 测试转换器配置 ===\n")
    
    config = load_config("config.yaml")
    if not config:
        print("❌ 无法加载配置文件")
        return False
    
    model_config = config.get('model', {})
    model_type = model_config.get('type', 'qwen')
    api_key = model_config.get('api_key')
    
    print(f"创建转换器:")
    print(f"  模型类型: {model_type}")
    print(f"  API密钥: {'已设置' if api_key else '未设置'}")
    
    try:
        converter = CribNetTextConverter(
            model_type=model_type,
            api_key=api_key,
            use_llm_guidance=True,
            config=config
        )
        
        print("✓ 转换器创建成功")
        
        # 显示转换器配置
        print(f"  实际模型名称: {converter.model_name}")
        print(f"  最大tokens: {converter.max_tokens}")
        print(f"  温度: {converter.temperature}")
        
        # 检查模型名称是否合适
        if converter.model_name in ['qwen-turbo', 'qwen-turbo-0919', 'qwen-turbo-1101']:
            print("⚠️  注意: 配置的是文本模型，图像处理时会自动切换到多模态模型")
        
        return True
        
    except Exception as e:
        print(f"❌ 转换器创建失败: {str(e)}")
        return False

def test_dataset_paths():
    """测试数据集路径"""
    print("\n=== 测试数据集路径 ===\n")
    
    config = load_config("config.yaml")
    if not config:
        return False
    
    dataset_config = config.get('dataset', {})
    source_path = dataset_config.get('source_path')
    output_path = dataset_config.get('output_path')
    
    print(f"检查数据集路径:")
    print(f"  源路径: {source_path}")
    
    if source_path and os.path.exists(source_path):
        print("  ✓ 源路径存在")
        
        # 检查子集
        subsets = ['CRIBHD-T', 'CRIBHD-B', 'CRIBHD-C']
        for subset in subsets:
            subset_path = Path(source_path) / subset
            if subset_path.exists():
                print(f"    ✓ 找到子集: {subset}")
            else:
                print(f"    ❌ 缺少子集: {subset}")
    else:
        print("  ❌ 源路径不存在")
    
    print(f"  输出路径: {output_path}")
    if output_path:
        output_dir = Path(output_path).parent
        if output_dir.exists():
            print("  ✓ 输出目录可访问")
        else:
            print("  ⚠️  输出目录不存在，将自动创建")
    
    return True

def test_api_connection():
    """测试API连接"""
    print("\n=== 测试API连接 ===\n")
    
    config = load_config("config.yaml")
    if not config:
        return False
    
    model_config = config.get('model', {})
    api_key = model_config.get('api_key')
    
    if not api_key:
        print("❌ 未设置API密钥")
        return False
    
    print(f"API密钥: {api_key[:8]}...{api_key[-8:]}")
    
    # 简单的API连接测试
    try:
        import dashscope
        dashscope.api_key = api_key
        
        # 尝试一个简单的文本生成请求来测试连接
        from dashscope import Generation
        
        response = Generation.call(
            model='qwen-turbo',
            prompt='测试连接',
            max_tokens=10
        )
        
        if response.status_code == 200:
            print("✓ API连接正常")
            return True
        else:
            print(f"❌ API连接失败: {response.message}")
            return False
            
    except ImportError:
        print("⚠️  dashscope库未安装，无法测试API连接")
        print("请运行: pip install dashscope")
        return False
    except Exception as e:
        print(f"❌ API连接测试失败: {str(e)}")
        return False

def show_usage_examples():
    """显示使用示例"""
    print("\n=== 使用示例 ===\n")
    
    print("1. 使用配置文件运行:")
    print("   python3 cribnet_to_text_converter.py --config config.yaml")
    print()
    
    print("2. 覆盖配置文件中的特定参数:")
    print("   python3 cribnet_to_text_converter.py --config config.yaml --max_images 10")
    print()
    
    print("3. 只处理特定子集:")
    print("   python3 cribnet_to_text_converter.py --config config.yaml --subset cribhd_c")
    print()
    
    print("4. 启用LLM增强指导:")
    print("   python3 cribnet_to_text_converter.py --config config.yaml --use_llm_guidance")
    print()

def main():
    """主函数"""
    print("CribHD配置文件测试工具")
    print("=" * 40)
    
    tests = [
        ("配置文件加载", test_config_loading),
        ("转换器配置", test_converter_with_config),
        ("数据集路径", test_dataset_paths),
        ("API连接", test_api_connection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"执行测试: {test_name}")
        print('='*50)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示使用示例
    show_usage_examples()
    
    # 测试总结
    print(f"\n{'='*50}")
    print("测试总结")
    print('='*50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 配置文件功能正常！可以使用配置文件运行程序了。")
    else:
        print("⚠️  部分功能需要检查，请根据错误信息进行调整。")

if __name__ == "__main__":
    main()
