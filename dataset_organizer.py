#!/usr/bin/env python3
"""
CribHD婴幼儿监测文本数据集整理工具
用于优化和标准化数据集格式，生成统计报告
"""

import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import re
from collections import Counter

class DatasetOrganizer:
    """数据集整理器"""
    
    def __init__(self, dataset_path: str):
        """
        初始化数据集整理器
        
        Args:
            dataset_path: 数据集根目录路径
        """
        self.dataset_path = Path(dataset_path)
        self.output_path = self.dataset_path
        
    def load_dataset(self) -> Dict[str, Any]:
        """加载完整数据集"""
        json_file = self.dataset_path / "cribhd_text_dataset.json"
        
        if not json_file.exists():
            raise FileNotFoundError(f"数据集文件不存在: {json_file}")
            
        with open(json_file, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def analyze_dataset(self, dataset: Dict[str, Any]) -> Dict[str, Any]:
        """分析数据集并生成详细统计"""
        data = dataset.get('data', [])
        
        # 基础统计
        total_samples = len(data)
        
        # 年龄分布分析
        age_distribution = Counter()
        motor_skills_dist = Counter()
        safety_risks_dist = Counter()
        hazard_categories = Counter()
        development_levels = Counter()
        
        # 文本长度统计
        text_lengths = []
        
        for item in data:
            # 年龄分析
            age = item.get('estimated_age_months', 0)
            if age <= 3:
                age_group = "0-3个月"
            elif age <= 6:
                age_group = "3-6个月"
            elif age <= 12:
                age_group = "6-12个月"
            elif age <= 24:
                age_group = "12-24个月"
            else:
                age_group = "24个月以上"
            age_distribution[age_group] += 1
            
            # 运动技能分析
            motor_skills = item.get('motor_skills', [])
            for skill in motor_skills:
                motor_skills_dist[skill] += 1
            
            # 安全风险分析
            safety_assessment = item.get('safety_assessment', {})
            risks = safety_assessment.get('risks_detected', [])
            for risk in risks:
                safety_risks_dist[risk] += 1
            
            # 发育水平分析
            dev_level = item.get('development_level', '未知')
            development_levels[dev_level] += 1
            
            # 文本长度分析
            raw_desc = item.get('raw_description', [])
            if raw_desc and isinstance(raw_desc, list) and len(raw_desc) > 0:
                text = raw_desc[0].get('text', '')
                text_lengths.append(len(text))
            
            # 从注释中提取危险类别
            annotations = item.get('annotations', {})
            # 这里可以根据实际的注释结构来提取类别信息
        
        # 计算文本长度统计
        if text_lengths:
            avg_length = sum(text_lengths) / len(text_lengths)
            min_length = min(text_lengths)
            max_length = max(text_lengths)
        else:
            avg_length = min_length = max_length = 0
        
        return {
            "基础统计": {
                "总样本数": total_samples,
                "分析时间": datetime.now().isoformat(),
                "数据集版本": "1.0"
            },
            "年龄分布": dict(age_distribution),
            "运动技能分布": dict(motor_skills_dist.most_common(10)),
            "安全风险分布": dict(safety_risks_dist.most_common(10)),
            "发育水平分布": dict(development_levels),
            "文本统计": {
                "平均长度": round(avg_length, 2),
                "最短长度": min_length,
                "最长长度": max_length,
                "总文本数": len(text_lengths)
            }
        }
    
    def extract_structured_info(self, text: str) -> Dict[str, Any]:
        """从文本中提取结构化信息"""
        structured = {
            "age_estimate": None,
            "motor_skills": [],
            "safety_risks": [],
            "development_level": "正常",
            "scene_objects": []
        }
        
        # 提取年龄信息
        age_pattern = r'(\d+)-?(\d+)?个月'
        age_match = re.search(age_pattern, text)
        if age_match:
            age_start = int(age_match.group(1))
            structured["age_estimate"] = age_start
        
        # 提取运动技能
        motor_skills = [
            "抬头", "翻身", "坐立", "爬行", "站立", "行走",
            "抓握", "钳形抓握", "指点", "拍手", "挥手"
        ]
        for skill in motor_skills:
            if skill in text:
                structured["motor_skills"].append(skill)
        
        # 提取安全风险
        safety_risks = [
            "窒息风险", "小玩具", "覆盖面部", "缠绕", "尖锐物品",
            "不稳定", "跌落风险", "过热", "过冷"
        ]
        for risk in safety_risks:
            if risk in text:
                structured["safety_risks"].append(risk)
        
        # 提取发育水平
        if "超前" in text or "提前" in text:
            structured["development_level"] = "超前"
        elif "延迟" in text or "落后" in text:
            structured["development_level"] = "延迟"
        elif "正常" in text:
            structured["development_level"] = "正常"
        
        # 提取场景物品
        objects = ["婴儿", "毯子", "玩具", "奶瓶", "枕头", "床单"]
        for obj in objects:
            if obj in text:
                structured["scene_objects"].append(obj)
        
        return structured
    
    def enhance_dataset(self, dataset: Dict[str, Any]) -> Dict[str, Any]:
        """增强数据集，添加结构化信息"""
        enhanced_data = []
        
        for item in dataset.get('data', []):
            enhanced_item = item.copy()
            
            # 从文本中提取结构化信息
            raw_desc = item.get('raw_description', [])
            if raw_desc and isinstance(raw_desc, list) and len(raw_desc) > 0:
                text = raw_desc[0].get('text', '')
                structured_info = self.extract_structured_info(text)
                
                # 更新或添加结构化字段
                if not enhanced_item.get('motor_skills'):
                    enhanced_item['motor_skills'] = structured_info['motor_skills']
                
                if not enhanced_item.get('safety_assessment', {}).get('risks_detected'):
                    if 'safety_assessment' not in enhanced_item:
                        enhanced_item['safety_assessment'] = {}
                    enhanced_item['safety_assessment']['risks_detected'] = structured_info['safety_risks']
                
                if structured_info['age_estimate'] and not enhanced_item.get('estimated_age_months'):
                    enhanced_item['estimated_age_months'] = structured_info['age_estimate']
                
                # 添加场景物品信息
                enhanced_item['scene_objects'] = structured_info['scene_objects']
            
            enhanced_data.append(enhanced_item)
        
        # 更新数据集
        enhanced_dataset = dataset.copy()
        enhanced_dataset['data'] = enhanced_data
        enhanced_dataset['metadata']['enhanced'] = True
        enhanced_dataset['metadata']['enhancement_time'] = datetime.now().isoformat()
        
        return enhanced_dataset
    
    def generate_summary_report(self, analysis: Dict[str, Any]) -> str:
        """生成数据集摘要报告"""
        report = f"""
# CribHD婴幼儿监测文本数据集分析报告

## 数据集概览
- **总样本数**: {analysis['基础统计']['总样本数']:,}
- **分析时间**: {analysis['基础统计']['分析时间']}
- **数据集版本**: {analysis['基础统计']['数据集版本']}

## 年龄分布
"""
        for age_group, count in analysis['年龄分布'].items():
            percentage = (count / analysis['基础统计']['总样本数']) * 100
            report += f"- **{age_group}**: {count:,} 样本 ({percentage:.1f}%)\n"
        
        report += f"""
## 文本统计
- **平均文本长度**: {analysis['文本统计']['平均长度']:,} 字符
- **最短文本**: {analysis['文本统计']['最短长度']:,} 字符  
- **最长文本**: {analysis['文本统计']['最长长度']:,} 字符

## 发育水平分布
"""
        for level, count in analysis['发育水平分布'].items():
            percentage = (count / analysis['基础统计']['总样本数']) * 100
            report += f"- **{level}**: {count:,} 样本 ({percentage:.1f}%)\n"
        
        if analysis['运动技能分布']:
            report += "\n## 主要运动技能 (Top 10)\n"
            for skill, count in list(analysis['运动技能分布'].items())[:10]:
                report += f"- **{skill}**: {count:,} 次\n"
        
        if analysis['安全风险分布']:
            report += "\n## 主要安全风险 (Top 10)\n"
            for risk, count in list(analysis['安全风险分布'].items())[:10]:
                report += f"- **{risk}**: {count:,} 次\n"
        
        return report
    
    def organize_dataset(self):
        """执行完整的数据集整理流程"""
        print("🚀 开始整理数据集...")
        
        # 1. 加载数据集
        print("📂 加载数据集...")
        dataset = self.load_dataset()
        
        # 2. 分析数据集
        print("📊 分析数据集...")
        analysis = self.analyze_dataset(dataset)
        
        # 3. 增强数据集
        print("🔧 增强数据集...")
        enhanced_dataset = self.enhance_dataset(dataset)
        
        # 4. 保存增强后的数据集
        print("💾 保存增强数据集...")
        enhanced_file = self.output_path / "cribhd_text_dataset_enhanced.json"
        with open(enhanced_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_dataset, f, ensure_ascii=False, indent=2)
        
        # 5. 保存详细分析报告
        print("📋 生成分析报告...")
        analysis_file = self.output_path / "dataset_analysis.json"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            json.dump(analysis, f, ensure_ascii=False, indent=2)
        
        # 6. 生成摘要报告
        summary_report = self.generate_summary_report(analysis)
        summary_file = self.output_path / "DATASET_SUMMARY.md"
        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write(summary_report)
        
        print("✅ 数据集整理完成!")
        print(f"📁 输出文件:")
        print(f"   - 增强数据集: {enhanced_file}")
        print(f"   - 分析报告: {analysis_file}")
        print(f"   - 摘要报告: {summary_file}")
        
        return analysis

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CribHD数据集整理工具")
    parser.add_argument("--dataset_path", 
                       default="output/cribhd_text_dataset",
                       help="数据集路径")
    
    args = parser.parse_args()
    
    organizer = DatasetOrganizer(args.dataset_path)
    analysis = organizer.organize_dataset()
    
    print(f"\n📊 数据集统计摘要:")
    print(f"总样本数: {analysis['基础统计']['总样本数']:,}")
    print(f"平均文本长度: {analysis['文本统计']['平均长度']:.0f} 字符")
    print(f"年龄分布: {analysis['年龄分布']}")

if __name__ == "__main__":
    main()
