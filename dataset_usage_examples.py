#!/usr/bin/env python3
"""
CribHD婴幼儿监测文本数据集使用示例
展示如何加载和使用数据集进行各种任务
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any
import random

class DatasetUsageExamples:
    """数据集使用示例"""
    
    def __init__(self, dataset_path: str = "output/cribhd_text_dataset/final_release"):
        self.dataset_path = Path(dataset_path)
        
    def load_dataset_formats(self) -> Dict[str, Any]:
        """加载不同格式的数据集"""
        print("📂 加载数据集的不同格式...")
        
        data = {}
        
        # 1. 加载完整JSON格式
        json_file = self.dataset_path / "cribhd_text_dataset_final.json"
        if json_file.exists():
            with open(json_file, 'r', encoding='utf-8') as f:
                data['full_dataset'] = json.load(f)
            print(f"✅ 完整数据集: {len(data['full_dataset']['data'])} 样本")
        
        # 2. 加载训练格式
        train_file = self.dataset_path / "training_format" / "train.json"
        valid_file = self.dataset_path / "training_format" / "valid.json"
        test_file = self.dataset_path / "training_format" / "test.json"
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                data['train'] = json.load(f)
            print(f"✅ 训练集: {len(data['train'])} 样本")
                
        if valid_file.exists():
            with open(valid_file, 'r', encoding='utf-8') as f:
                data['valid'] = json.load(f)
            print(f"✅ 验证集: {len(data['valid'])} 样本")
                
        if test_file.exists():
            with open(test_file, 'r', encoding='utf-8') as f:
                data['test'] = json.load(f)
            print(f"✅ 测试集: {len(data['test'])} 样本")
        
        # 3. 加载CSV格式
        csv_file = self.dataset_path / "cribhd_text_dataset_final.csv"
        if csv_file.exists():
            data['csv'] = pd.read_csv(csv_file)
            print(f"✅ CSV格式: {len(data['csv'])} 行")
        
        return data
    
    def example_text_generation(self, data: Dict[str, Any]):
        """文本生成任务示例"""
        print("\n🎯 示例1: 文本生成任务")
        print("=" * 50)
        
        if 'train' not in data:
            print("❌ 训练数据不可用")
            return
            
        # 随机选择一个样本
        sample = random.choice(data['train'])
        text = sample['text'][0]['text']
        labels = sample['labels']
        
        print("📝 原始文本描述:")
        print(text[:300] + "..." if len(text) > 300 else text)
        
        print(f"\n🏷️ 标签信息:")
        print(f"   - 年龄: {labels['age_months']}个月")
        print(f"   - 发育水平: {labels['development_level']}")
        print(f"   - 危险类别: {labels['hazard_category']}")
        
        print(f"\n💡 文本生成任务示例:")
        print("   输入: '基于以下婴儿场景，生成发育评估报告：'")
        print("   目标: 生成类似上述格式的专业评估文本")
    
    def example_classification(self, data: Dict[str, Any]):
        """分类任务示例"""
        print("\n🎯 示例2: 安全风险分类任务")
        print("=" * 50)
        
        if 'train' not in data:
            print("❌ 训练数据不可用")
            return
        
        # 统计不同类别的分布
        hazard_categories = {}
        safety_risks = set()
        
        for sample in data['train'][:100]:  # 检查前100个样本
            category = sample['labels']['hazard_category']
            hazard_categories[category] = hazard_categories.get(category, 0) + 1
            
            risks = sample['labels']['safety_risks']
            safety_risks.update(risks)
        
        print("📊 危险类别分布:")
        for category, count in hazard_categories.items():
            print(f"   - {category}: {count} 样本")
        
        print(f"\n🚨 发现的安全风险类型: {len(safety_risks)} 种")
        if safety_risks:
            print("   示例风险:", list(safety_risks)[:5])
        
        print(f"\n💡 分类任务示例:")
        print("   输入: 婴儿场景描述文本")
        print("   输出: 危险类别 (blanket/toy/crisis_scene)")
    
    def example_question_answering(self, data: Dict[str, Any]):
        """问答任务示例"""
        print("\n🎯 示例3: 问答任务")
        print("=" * 50)
        
        if 'train' not in data:
            print("❌ 训练数据不可用")
            return
        
        # 选择一个有详细描述的样本
        sample = random.choice(data['train'])
        text = sample['text'][0]['text']
        labels = sample['labels']
        
        print("📖 场景描述:")
        print(text[:200] + "..." if len(text) > 200 else text)
        
        print(f"\n❓ 示例问题和答案:")
        
        questions_answers = [
            ("这个婴儿的年龄是多少？", f"{labels['age_months']}个月"),
            ("婴儿的发育水平如何？", labels['development_level']),
            ("这个场景属于什么类型？", labels['hazard_category']),
            ("有什么安全风险吗？", "是" if labels['safety_risks'] else "否")
        ]
        
        for q, a in questions_answers:
            print(f"   Q: {q}")
            print(f"   A: {a}")
            print()
    
    def example_data_analysis(self, data: Dict[str, Any]):
        """数据分析示例"""
        print("\n🎯 示例4: 数据分析")
        print("=" * 50)
        
        if 'csv' not in data:
            print("❌ CSV数据不可用")
            return
        
        df = data['csv']
        
        print("📊 数据集基础统计:")
        print(f"   - 总样本数: {len(df):,}")
        print(f"   - 平均文本长度: {df['text_length'].mean():.0f} 字符")
        print(f"   - 文本长度范围: {df['text_length'].min()} - {df['text_length'].max()}")
        
        print(f"\n📈 年龄分布:")
        age_dist = df['estimated_age_months'].value_counts().sort_index()
        for age, count in age_dist.head().items():
            print(f"   - {age}个月: {count} 样本")
        
        print(f"\n🏥 发育水平分布:")
        dev_dist = df['development_level'].value_counts()
        for level, count in dev_dist.items():
            print(f"   - {level}: {count} 样本")
    
    def example_huggingface_integration(self, data: Dict[str, Any]):
        """Hugging Face集成示例"""
        print("\n🎯 示例5: Hugging Face Datasets集成")
        print("=" * 50)
        
        print("📦 使用Hugging Face Datasets库加载数据:")
        print("""
from datasets import load_dataset

# 方法1: 从本地文件加载
dataset = load_dataset('json', data_files={
    'train': 'training_format/train.json',
    'validation': 'training_format/valid.json',
    'test': 'training_format/test.json'
})

# 方法2: 从CSV加载
dataset = load_dataset('csv', data_files='cribhd_text_dataset_final.csv')

# 查看数据集信息
print(dataset)
print(dataset['train'][0])
        """)
        
        print("🔧 数据预处理示例:")
        print("""
def preprocess_function(examples):
    # 提取文本内容
    texts = [item[0]['text'] for item in examples['text']]
    
    # 标记化
    tokenized = tokenizer(texts, truncation=True, padding=True)
    
    # 添加标签
    tokenized['labels'] = examples['labels']['age_months']
    
    return tokenized

# 应用预处理
tokenized_dataset = dataset.map(preprocess_function, batched=True)
        """)
    
    def example_model_training(self, data: Dict[str, Any]):
        """模型训练示例"""
        print("\n🎯 示例6: 模型训练框架")
        print("=" * 50)
        
        print("🤖 使用Transformers进行文本分类:")
        print("""
from transformers import (
    AutoTokenizer, AutoModelForSequenceClassification,
    TrainingArguments, Trainer
)

# 加载预训练模型
model_name = "bert-base-chinese"
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForSequenceClassification.from_pretrained(
    model_name, 
    num_labels=3  # blanket, toy, crisis_scene
)

# 训练参数
training_args = TrainingArguments(
    output_dir='./results',
    num_train_epochs=3,
    per_device_train_batch_size=16,
    per_device_eval_batch_size=64,
    warmup_steps=500,
    weight_decay=0.01,
    logging_dir='./logs',
)

# 创建训练器
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    eval_dataset=eval_dataset,
)

# 开始训练
trainer.train()
        """)
    
    def run_all_examples(self):
        """运行所有示例"""
        print("🚀 CribHD婴幼儿监测文本数据集使用示例")
        print("=" * 60)
        
        # 加载数据
        data = self.load_dataset_formats()
        
        # 运行各种示例
        self.example_text_generation(data)
        self.example_classification(data)
        self.example_question_answering(data)
        self.example_data_analysis(data)
        self.example_huggingface_integration(data)
        self.example_model_training(data)
        
        print("\n✅ 所有示例展示完成!")
        print("📚 更多使用方法请参考README.md文档")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CribHD数据集使用示例")
    parser.add_argument("--dataset_path", 
                       default="output/cribhd_text_dataset/final_release",
                       help="数据集路径")
    
    args = parser.parse_args()
    
    examples = DatasetUsageExamples(args.dataset_path)
    examples.run_all_examples()

if __name__ == "__main__":
    main()
