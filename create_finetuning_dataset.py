#!/usr/bin/env python3
"""
创建适合大模型微调的标准instruction格式数据集
支持多种主流微调框架格式：Alpaca、ChatML、ShareGPT等
"""

import json
import os
from typing import Dict, List, Any
from datetime import datetime

class FineTuningDatasetCreator:
    def __init__(self):
        self.system_prompt = "你是一位专业的婴幼儿发育评估和家庭安全监测专家，具有丰富的儿科医学知识和实践经验。"
        
    def create_alpaca_format(self, samples: List[Dict]) -> List[Dict]:
        """创建Alpaca格式的数据集"""
        alpaca_data = []
        
        for sample in samples:
            alpaca_sample = {
                "instruction": sample["instruction"],
                "input": sample["input"],
                "output": sample["output"]
            }
            alpaca_data.append(alpaca_sample)
            
        return alpaca_data
    
    def create_chatml_format(self, samples: List[Dict]) -> List[Dict]:
        """创建ChatML格式的数据集"""
        chatml_data = []
        
        for sample in samples:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": f"{sample['instruction']}\n\n{sample['input']}"},
                {"role": "assistant", "content": sample["output"]}
            ]
            
            chatml_sample = {
                "messages": messages
            }
            chatml_data.append(chatml_sample)
            
        return chatml_data
    
    def create_sharegpt_format(self, samples: List[Dict]) -> List[Dict]:
        """创建ShareGPT格式的数据集"""
        sharegpt_data = []
        
        for sample in samples:
            conversations = [
                {"from": "system", "value": self.system_prompt},
                {"from": "human", "value": f"{sample['instruction']}\n\n{sample['input']}"},
                {"from": "gpt", "value": sample["output"]}
            ]
            
            sharegpt_sample = {
                "conversations": conversations
            }
            sharegpt_data.append(sharegpt_sample)
            
        return sharegpt_data
    
    def create_llama_format(self, samples: List[Dict]) -> List[Dict]:
        """创建LLaMA格式的数据集"""
        llama_data = []
        
        for sample in samples:
            # LLaMA格式通常使用特殊的token
            text = f"<s>[INST] <<SYS>>\n{self.system_prompt}\n<</SYS>>\n\n{sample['instruction']}\n\n{sample['input']} [/INST] {sample['output']} </s>"
            
            llama_sample = {
                "text": text
            }
            llama_data.append(llama_sample)
            
        return llama_data
    
    def create_qwen_format(self, samples: List[Dict]) -> List[Dict]:
        """创建Qwen格式的数据集"""
        qwen_data = []
        
        for sample in samples:
            messages = [
                {"role": "system", "content": self.system_prompt},
                {"role": "user", "content": f"{sample['instruction']}\n\n{sample['input']}"},
                {"role": "assistant", "content": sample["output"]}
            ]
            
            qwen_sample = {
                "type": "chatml",
                "messages": messages
            }
            qwen_data.append(qwen_sample)
            
        return qwen_data
    
    def load_instruction_data(self, file_path: str) -> List[Dict]:
        """加载instruction格式的数据"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, dict) and 'data' in data:
            return data['data']
        elif isinstance(data, list):
            return data
        else:
            raise ValueError("不支持的数据格式")
    
    def create_all_formats(self, input_dir: str, output_dir: str):
        """创建所有格式的微调数据集"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 加载训练、验证、测试数据
        splits = ['train', 'valid', 'test']
        
        for split in splits:
            input_file = os.path.join(input_dir, f'{split}_instruction.json')
            if not os.path.exists(input_file):
                print(f"警告: 文件不存在 {input_file}")
                continue
                
            print(f"正在处理 {split} 数据...")
            samples = self.load_instruction_data(input_file)
            
            # 创建各种格式
            formats = {
                'alpaca': self.create_alpaca_format(samples),
                'chatml': self.create_chatml_format(samples),
                'sharegpt': self.create_sharegpt_format(samples),
                'llama': self.create_llama_format(samples),
                'qwen': self.create_qwen_format(samples)
            }
            
            # 保存各种格式
            for format_name, format_data in formats.items():
                format_dir = os.path.join(output_dir, format_name)
                os.makedirs(format_dir, exist_ok=True)
                
                output_file = os.path.join(format_dir, f'{split}.json')
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(format_data, f, ensure_ascii=False, indent=2)
                
                print(f"  {format_name}格式已保存: {len(format_data)} 样本 -> {output_file}")
        
        # 创建使用说明
        self.create_usage_guide(output_dir)
        
        print("所有格式的微调数据集创建完成！")
    
    def create_usage_guide(self, output_dir: str):
        """创建使用说明文档"""
        guide_content = """# CribHD婴幼儿监测数据集 - 微调格式说明

## 数据集概述
本数据集提供了多种主流大模型微调框架支持的格式，包含婴幼儿发育评估和安全监测的专业知识。

## 格式说明

### 1. Alpaca格式 (`alpaca/`)
适用于: Stanford Alpaca, Alpaca-LoRA等
```json
{
  "instruction": "指令内容",
  "input": "输入内容", 
  "output": "期望输出"
}
```

### 2. ChatML格式 (`chatml/`)
适用于: OpenAI ChatGPT, GPT-4等
```json
{
  "messages": [
    {"role": "system", "content": "系统提示"},
    {"role": "user", "content": "用户输入"},
    {"role": "assistant", "content": "助手回复"}
  ]
}
```

### 3. ShareGPT格式 (`sharegpt/`)
适用于: FastChat, Vicuna等
```json
{
  "conversations": [
    {"from": "system", "value": "系统提示"},
    {"from": "human", "value": "人类输入"},
    {"from": "gpt", "value": "GPT回复"}
  ]
}
```

### 4. LLaMA格式 (`llama/`)
适用于: LLaMA, LLaMA2等
```json
{
  "text": "<s>[INST] <<SYS>>系统提示<</SYS>>用户输入 [/INST] 助手回复 </s>"
}
```

### 5. Qwen格式 (`qwen/`)
适用于: Qwen, Qwen-Chat等
```json
{
  "type": "chatml",
  "messages": [
    {"role": "system", "content": "系统提示"},
    {"role": "user", "content": "用户输入"},
    {"role": "assistant", "content": "助手回复"}
  ]
}
```

## 使用示例

### 使用Alpaca格式进行微调
```python
from datasets import load_dataset

# 加载数据集
dataset = load_dataset('json', data_files={
    'train': 'alpaca/train.json',
    'validation': 'alpaca/valid.json',
    'test': 'alpaca/test.json'
})

# 查看样本
print(dataset['train'][0])
```

### 使用ChatML格式
```python
import json

# 加载ChatML格式数据
with open('chatml/train.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 处理消息格式
for sample in data:
    messages = sample['messages']
    # 进行微调训练...
```

## 数据集统计
- 训练集: ~7,710 样本
- 验证集: ~963 样本  
- 测试集: ~965 样本
- 总计: ~9,638 样本

## 任务类型
1. 综合场景分析
2. 基础场景描述
3. 运动发育评估
4. 安全风险评估
5. 育儿指导建议
6. 专业总结

## 注意事项
1. 所有格式的数据内容完全一致，仅格式不同
2. 建议根据使用的微调框架选择对应格式
3. 数据集基于专业医学标准，适合医疗健康领域应用
4. 使用时请遵循Apache 2.0许可证

## 联系方式
如有问题或建议，请联系项目维护者。
"""
        
        guide_file = os.path.join(output_dir, 'README.md')
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"使用说明已保存到: {guide_file}")

def main():
    creator = FineTuningDatasetCreator()
    
    # 输入和输出路径
    input_dir = "output/cribhd_text_dataset/instruction_format"
    output_dir = "output/cribhd_text_dataset/finetuning_formats"
    
    # 检查输入目录是否存在
    if not os.path.exists(input_dir):
        print(f"错误: 输入目录不存在 {input_dir}")
        print("请先运行 convert_to_instruction_format.py 生成instruction格式数据")
        return
    
    # 创建所有格式的微调数据集
    creator.create_all_formats(input_dir, output_dir)

if __name__ == "__main__":
    main()
