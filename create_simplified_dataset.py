#!/usr/bin/env python3
"""
创建简化的instruction-input-output格式数据集，专门用于大模型微调
减少数据冗余，提高训练效率
"""

import json
import os
from typing import Dict, List, Any
from datetime import datetime
import random

class SimplifiedDatasetCreator:
    def __init__(self):
        self.system_prompt = "你是一位专业的婴幼儿发育评估和家庭安全监测专家。"
        
    def load_original_data(self, file_path: str) -> List[Dict]:
        """加载原始数据集"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return data['data']
    
    def create_simplified_samples(self, original_data: List[Dict]) -> List[Dict]:
        """创建简化的训练样本"""
        simplified_samples = []
        
        # 定义不同的任务类型和对应的指令
        task_templates = {
            "comprehensive": {
                "instruction": "请分析这张婴儿床场景图像，提供专业的发育评估和安全监测分析。",
                "extract_full": True
            },
            "development": {
                "instruction": "请评估图像中婴儿的运动发育水平。",
                "extract_section": "运动发育评估"
            },
            "safety": {
                "instruction": "请分析这张婴儿床场景的安全风险。",
                "extract_section": "安全风险评估"
            },
            "guidance": {
                "instruction": "请为家长提供育儿指导建议。",
                "extract_section": "育儿指导建议"
            },
            "summary": {
                "instruction": "请对婴儿的整体发育状况进行总结。",
                "extract_section": "专业总结"
            }
        }
        
        for sample in original_data:
            if not sample.get('raw_description') or not sample['raw_description']:
                continue
                
            full_text = sample['raw_description'][0]['text']
            
            # 构建输入信息
            input_parts = []
            if sample.get('estimated_age_months'):
                input_parts.append(f"婴儿年龄: {sample['estimated_age_months']}个月")
            
            # 添加场景类型信息
            scene_type_map = {
                'crib': '婴儿床场景',
                'blanket': '毯子场景', 
                'toy': '玩具场景',
                'crisis': '危机场景'
            }
            scene_type = scene_type_map.get(sample.get('scene_type', 'crib'), '婴儿床场景')
            input_parts.append(f"场景类型: {scene_type}")
            
            input_text = "场景信息: " + ", ".join(input_parts)
            
            # 解析文本段落
            sections = self.parse_sections(full_text)
            
            # 为每个样本创建1-2个训练样本（避免过度扩充）
            selected_tasks = random.sample(list(task_templates.keys()), 
                                         min(2, len(task_templates)))
            
            for task_type in selected_tasks:
                template = task_templates[task_type]
                
                if template.get("extract_full"):
                    output_text = full_text
                else:
                    section_name = template.get("extract_section")
                    if section_name in sections:
                        output_text = sections[section_name]
                    else:
                        continue  # 跳过没有对应段落的样本
                
                # 创建训练样本
                training_sample = {
                    "instruction": template["instruction"],
                    "input": input_text,
                    "output": output_text.strip()
                }
                
                simplified_samples.append(training_sample)
        
        return simplified_samples
    
    def parse_sections(self, text: str) -> Dict[str, str]:
        """解析文本中的各个部分"""
        sections = {}
        current_section = None
        current_content = []
        
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('## '):
                # 保存前一个部分
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                
                # 开始新部分
                section_title = line.replace('## ', '').split('.', 1)
                if len(section_title) > 1:
                    current_section = section_title[1].strip()
                else:
                    current_section = section_title[0].strip()
                current_content = []
            elif line and current_section:
                current_content.append(line)
        
        # 保存最后一个部分
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()
            
        return sections
    
    def create_dataset_splits(self, samples: List[Dict], output_dir: str):
        """创建数据集分割"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 打乱数据
        random.seed(42)
        shuffled_samples = samples.copy()
        random.shuffle(shuffled_samples)
        
        total = len(shuffled_samples)
        train_size = int(total * 0.8)
        valid_size = int(total * 0.1)
        
        splits = {
            'train': shuffled_samples[:train_size],
            'valid': shuffled_samples[train_size:train_size + valid_size],
            'test': shuffled_samples[train_size + valid_size:]
        }
        
        # 保存各个分割
        for split_name, split_data in splits.items():
            # Alpaca格式
            alpaca_file = os.path.join(output_dir, f'{split_name}_alpaca.json')
            with open(alpaca_file, 'w', encoding='utf-8') as f:
                json.dump(split_data, f, ensure_ascii=False, indent=2)
            
            # ChatML格式
            chatml_data = []
            for sample in split_data:
                chatml_sample = {
                    "messages": [
                        {"role": "system", "content": self.system_prompt},
                        {"role": "user", "content": f"{sample['instruction']}\n\n{sample['input']}"},
                        {"role": "assistant", "content": sample["output"]}
                    ]
                }
                chatml_data.append(chatml_sample)
            
            chatml_file = os.path.join(output_dir, f'{split_name}_chatml.json')
            with open(chatml_file, 'w', encoding='utf-8') as f:
                json.dump(chatml_data, f, ensure_ascii=False, indent=2)
            
            print(f"{split_name}集: {len(split_data)} 样本")
            print(f"  Alpaca格式: {alpaca_file}")
            print(f"  ChatML格式: {chatml_file}")
        
        return splits
    
    def generate_statistics(self, samples: List[Dict], output_dir: str):
        """生成数据集统计信息"""
        stats = {
            "数据集信息": {
                "总样本数": len(samples),
                "创建时间": datetime.now().isoformat(),
                "格式": "instruction-input-output"
            },
            "指令统计": {},
            "长度统计": {
                "平均输入长度": 0,
                "平均输出长度": 0,
                "输入长度范围": [float('inf'), 0],
                "输出长度范围": [float('inf'), 0]
            }
        }
        
        input_lengths = []
        output_lengths = []
        
        for sample in samples:
            # 统计指令类型
            instruction = sample['instruction']
            stats["指令统计"][instruction] = stats["指令统计"].get(instruction, 0) + 1
            
            # 统计长度
            input_len = len(sample['input'])
            output_len = len(sample['output'])
            
            input_lengths.append(input_len)
            output_lengths.append(output_len)
            
            # 更新范围
            stats["长度统计"]["输入长度范围"][0] = min(stats["长度统计"]["输入长度范围"][0], input_len)
            stats["长度统计"]["输入长度范围"][1] = max(stats["长度统计"]["输入长度范围"][1], input_len)
            stats["长度统计"]["输出长度范围"][0] = min(stats["长度统计"]["输出长度范围"][0], output_len)
            stats["长度统计"]["输出长度范围"][1] = max(stats["长度统计"]["输出长度范围"][1], output_len)
        
        stats["长度统计"]["平均输入长度"] = sum(input_lengths) / len(input_lengths)
        stats["长度统计"]["平均输出长度"] = sum(output_lengths) / len(output_lengths)
        
        # 保存统计信息
        stats_file = os.path.join(output_dir, 'dataset_statistics.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n数据集统计:")
        print(f"  总样本数: {stats['数据集信息']['总样本数']}")
        print(f"  平均输入长度: {stats['长度统计']['平均输入长度']:.1f} 字符")
        print(f"  平均输出长度: {stats['长度统计']['平均输出长度']:.1f} 字符")
        print(f"  指令类型数: {len(stats['指令统计'])}")
        print(f"  统计信息保存到: {stats_file}")
    
    def create_usage_example(self, output_dir: str):
        """创建使用示例"""
        example_code = '''# CribHD婴幼儿监测数据集使用示例

## 1. 使用Alpaca格式进行微调

```python
import json
from datasets import Dataset

# 加载训练数据
with open('train_alpaca.json', 'r', encoding='utf-8') as f:
    train_data = json.load(f)

# 转换为HuggingFace Dataset
dataset = Dataset.from_list(train_data)

# 查看样本
print("样本示例:")
print(f"指令: {dataset[0]['instruction']}")
print(f"输入: {dataset[0]['input']}")
print(f"输出: {dataset[0]['output'][:100]}...")

# 使用transformers进行微调
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer

model_name = "your-base-model"  # 替换为您的基础模型
tokenizer = AutoTokenizer.from_pretrained(model_name)
model = AutoModelForCausalLM.from_pretrained(model_name)

# 数据预处理
def preprocess_function(examples):
    inputs = [f"指令: {inst}\\n输入: {inp}\\n输出: " 
              for inst, inp in zip(examples['instruction'], examples['input'])]
    targets = examples['output']
    
    model_inputs = tokenizer(inputs, max_length=512, truncation=True, padding=True)
    labels = tokenizer(targets, max_length=512, truncation=True, padding=True)
    
    model_inputs["labels"] = labels["input_ids"]
    return model_inputs

# 处理数据集
tokenized_dataset = dataset.map(preprocess_function, batched=True)

# 训练参数
training_args = TrainingArguments(
    output_dir="./cribhd-finetuned",
    num_train_epochs=3,
    per_device_train_batch_size=4,
    gradient_accumulation_steps=2,
    warmup_steps=100,
    logging_steps=10,
    save_steps=500,
    evaluation_strategy="steps",
    eval_steps=500,
    save_total_limit=2,
    load_best_model_at_end=True,
)

# 创建训练器
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_dataset,
    tokenizer=tokenizer,
)

# 开始训练
trainer.train()
```

## 2. 使用ChatML格式

```python
import json

# 加载ChatML格式数据
with open('train_chatml.json', 'r', encoding='utf-8') as f:
    chatml_data = json.load(f)

# 查看消息格式
sample = chatml_data[0]
for message in sample['messages']:
    print(f"{message['role']}: {message['content'][:100]}...")
```

## 数据集特点

- **专业性**: 基于医学标准的婴幼儿发育评估
- **实用性**: 涵盖发育评估、安全监测、育儿指导等实际应用场景
- **高质量**: 经过质量控制和专业验证
- **多格式**: 支持主流微调框架

## 注意事项

1. 建议使用医疗领域预训练模型作为基础模型
2. 根据具体应用场景调整训练参数
3. 使用验证集监控训练过程，避免过拟合
4. 遵循相关医疗AI应用的伦理和法规要求
'''
        
        example_file = os.path.join(output_dir, 'usage_example.md')
        with open(example_file, 'w', encoding='utf-8') as f:
            f.write(example_code)
        
        print(f"使用示例保存到: {example_file}")

def main():
    creator = SimplifiedDatasetCreator()
    
    # 输入和输出路径
    input_file = "output/cribhd_text_dataset/final_release/cribhd_text_dataset_final.json"
    output_dir = "output/cribhd_text_dataset/simplified_finetuning"
    
    # 检查输入文件
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 {input_file}")
        return
    
    print("正在创建简化的微调数据集...")
    
    # 加载原始数据
    original_data = creator.load_original_data(input_file)
    print(f"加载原始数据: {len(original_data)} 个样本")
    
    # 创建简化样本
    simplified_samples = creator.create_simplified_samples(original_data)
    print(f"生成简化样本: {len(simplified_samples)} 个")
    
    # 创建数据集分割
    splits = creator.create_dataset_splits(simplified_samples, output_dir)
    
    # 生成统计信息
    creator.generate_statistics(simplified_samples, output_dir)
    
    # 创建使用示例
    creator.create_usage_example(output_dir)
    
    print("\n简化微调数据集创建完成！")
    print(f"输出目录: {output_dir}")

if __name__ == "__main__":
    main()
