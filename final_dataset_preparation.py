#!/usr/bin/env python3
"""
CribHD数据集最终整理脚本
修复数据质量问题，生成最终发布版本
"""

import json
import pandas as pd
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any
import shutil

class FinalDatasetPreparation:
    """最终数据集准备器"""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = Path(dataset_path)
        self.output_path = self.dataset_path / "final_release"
        self.output_path.mkdir(exist_ok=True)
        
    def load_and_clean_dataset(self) -> Dict[str, Any]:
        """加载并清理数据集"""
        print("📂 加载原始数据集...")
        
        json_file = self.dataset_path / "cribhd_text_dataset.json"
        with open(json_file, 'r', encoding='utf-8') as f:
            dataset = json.load(f)
        
        print("🧹 清理数据...")
        cleaned_data = []
        removed_count = 0
        
        for item in dataset.get('data', []):
            # 检查并修复raw_description字段
            raw_desc = item.get('raw_description')
            
            # 如果raw_description不是列表或为空，尝试修复
            if not isinstance(raw_desc, list):
                if isinstance(raw_desc, str) and raw_desc.strip():
                    item['raw_description'] = [{"text": raw_desc}]
                elif isinstance(raw_desc, dict) and 'text' in raw_desc:
                    item['raw_description'] = [raw_desc]
                else:
                    print(f"⚠️ 跳过无效样本: {item.get('annotations', {}).get('image_info', {}).get('file_name', 'unknown')}")
                    removed_count += 1
                    continue
            
            # 检查文本内容
            if raw_desc and len(raw_desc) > 0:
                if isinstance(raw_desc[0], dict):
                    text = raw_desc[0].get('text', '')
                elif isinstance(raw_desc[0], str):
                    text = raw_desc[0]
                else:
                    text = ''

                if len(text.strip()) < 50:  # 文本太短
                    print(f"⚠️ 跳过文本过短的样本")
                    removed_count += 1
                    continue
            
            # 确保必需字段存在
            if 'estimated_age_months' not in item:
                item['estimated_age_months'] = 6  # 默认值
            
            if 'development_level' not in item:
                item['development_level'] = "正常"
                
            if 'motor_skills' not in item:
                item['motor_skills'] = []
                
            if 'safety_assessment' not in item:
                item['safety_assessment'] = {
                    "risks_detected": [],
                    "safe_items": [],
                    "recommendations": []
                }
            
            cleaned_data.append(item)
        
        print(f"✅ 清理完成，移除了{removed_count}个问题样本")
        print(f"📊 最终样本数: {len(cleaned_data)}")
        
        # 更新数据集
        dataset['data'] = cleaned_data
        dataset['metadata']['cleaned'] = True
        dataset['metadata']['final_sample_count'] = len(cleaned_data)
        dataset['metadata']['removed_samples'] = removed_count
        dataset['metadata']['final_preparation_time'] = datetime.now().isoformat()
        
        return dataset
    
    def create_training_splits(self, dataset: Dict[str, Any]) -> Dict[str, List]:
        """创建训练数据分割"""
        print("📊 创建训练数据分割...")
        
        data = dataset.get('data', [])
        total_samples = len(data)
        
        # 分割比例
        train_ratio = 0.8
        valid_ratio = 0.1
        test_ratio = 0.1
        
        train_size = int(total_samples * train_ratio)
        valid_size = int(total_samples * valid_ratio)
        
        # 分割数据
        train_data = data[:train_size]
        valid_data = data[train_size:train_size + valid_size]
        test_data = data[train_size + valid_size:]
        
        print(f"📈 训练集: {len(train_data)} 样本")
        print(f"📈 验证集: {len(valid_data)} 样本") 
        print(f"📈 测试集: {len(test_data)} 样本")
        
        # 转换为训练格式
        def convert_to_training_format(items):
            training_items = []
            for item in items:
                raw_desc = item.get('raw_description', [])
                text = raw_desc[0].get('text', '') if raw_desc else ''
                
                # 从注释中提取危险类别
                hazard_category = "unknown"
                annotations = item.get('annotations', {})
                image_info = annotations.get('image_info', {})
                file_name = image_info.get('file_name', '')
                
                # 根据文件名或内容推断类别
                if 'blanket' in text.lower() or '毯子' in text:
                    hazard_category = "blanket"
                elif 'toy' in text.lower() or '玩具' in text:
                    hazard_category = "toy"
                elif 'crisis' in text.lower() or '危机' in text:
                    hazard_category = "crisis_scene"
                
                training_item = {
                    "text": [{"text": text}],
                    "labels": {
                        "age_months": item.get('estimated_age_months', 6),
                        "motor_skills": item.get('motor_skills', []),
                        "safety_risks": item.get('safety_assessment', {}).get('risks_detected', []),
                        "hazard_category": hazard_category,
                        "development_level": item.get('development_level', '正常')
                    },
                    "metadata": {
                        "image_id": image_info.get('file_name', ''),
                        "scene_type": item.get('scene_type', 'crib')
                    }
                }
                training_items.append(training_item)
            return training_items
        
        return {
            'train': convert_to_training_format(train_data),
            'valid': convert_to_training_format(valid_data),
            'test': convert_to_training_format(test_data)
        }
    
    def create_csv_export(self, dataset: Dict[str, Any]) -> pd.DataFrame:
        """创建CSV导出"""
        print("📋 创建CSV格式...")
        
        rows = []
        for item in dataset.get('data', []):
            raw_desc = item.get('raw_description', [])
            text = raw_desc[0].get('text', '') if raw_desc else ''
            
            # 提取图像信息
            annotations = item.get('annotations', {})
            image_info = annotations.get('image_info', {})
            
            row = {
                'image_id': image_info.get('file_name', '').replace('.jpg', ''),
                'image_path': f"CribHD/{image_info.get('file_name', '')}",
                'estimated_age_months': item.get('estimated_age_months', 6),
                'motor_skills': ','.join(item.get('motor_skills', [])),
                'development_level': item.get('development_level', '正常'),
                'safety_risks': ','.join(item.get('safety_assessment', {}).get('risks_detected', [])),
                'scene_type': item.get('scene_type', 'crib'),
                'text_length': len(text),
                'raw_description': text[:500] + '...' if len(text) > 500 else text  # 截断长文本
            }
            rows.append(row)
        
        return pd.DataFrame(rows)
    
    def generate_final_statistics(self, dataset: Dict[str, Any], splits: Dict[str, List]) -> Dict[str, Any]:
        """生成最终统计信息"""
        data = dataset.get('data', [])
        
        # 基础统计
        stats = {
            "数据集信息": {
                "版本": "1.0-final",
                "创建时间": datetime.now().isoformat(),
                "总样本数": len(data),
                "训练集样本数": len(splits['train']),
                "验证集样本数": len(splits['valid']),
                "测试集样本数": len(splits['test'])
            },
            "质量指标": {
                "平均文本长度": 0,
                "最短文本长度": 0,
                "最长文本长度": 0,
                "完整样本比例": 0
            },
            "内容分布": {
                "年龄分布": {},
                "发育水平分布": {},
                "场景类型分布": {}
            }
        }
        
        # 计算文本长度统计
        text_lengths = []
        complete_samples = 0
        
        for item in data:
            raw_desc = item.get('raw_description', [])
            if raw_desc and len(raw_desc) > 0:
                text = raw_desc[0].get('text', '')
                text_lengths.append(len(text))
                
                # 检查样本完整性
                if (len(text) > 100 and 
                    item.get('estimated_age_months') and
                    item.get('development_level')):
                    complete_samples += 1
        
        if text_lengths:
            stats["质量指标"]["平均文本长度"] = round(sum(text_lengths) / len(text_lengths), 2)
            stats["质量指标"]["最短文本长度"] = min(text_lengths)
            stats["质量指标"]["最长文本长度"] = max(text_lengths)
        
        stats["质量指标"]["完整样本比例"] = round(complete_samples / len(data) * 100, 2)
        
        # 内容分布统计
        from collections import Counter
        
        ages = [item.get('estimated_age_months', 0) for item in data]
        age_groups = []
        for age in ages:
            if age <= 3:
                age_groups.append("0-3个月")
            elif age <= 6:
                age_groups.append("3-6个月")
            elif age <= 12:
                age_groups.append("6-12个月")
            else:
                age_groups.append("12个月以上")
        
        stats["内容分布"]["年龄分布"] = dict(Counter(age_groups))
        stats["内容分布"]["发育水平分布"] = dict(Counter([item.get('development_level', '未知') for item in data]))
        stats["内容分布"]["场景类型分布"] = dict(Counter([item.get('scene_type', '未知') for item in data]))
        
        return stats
    
    def prepare_final_dataset(self):
        """准备最终数据集"""
        print("🚀 开始准备最终数据集...")
        
        # 1. 清理数据集
        cleaned_dataset = self.load_and_clean_dataset()
        
        # 2. 创建训练分割
        training_splits = self.create_training_splits(cleaned_dataset)
        
        # 3. 创建CSV导出
        csv_data = self.create_csv_export(cleaned_dataset)
        
        # 4. 生成最终统计
        final_stats = self.generate_final_statistics(cleaned_dataset, training_splits)
        
        # 5. 保存所有文件
        print("💾 保存最终文件...")
        
        # 保存清理后的完整数据集
        with open(self.output_path / "cribhd_text_dataset_final.json", 'w', encoding='utf-8') as f:
            json.dump(cleaned_dataset, f, ensure_ascii=False, indent=2)
        
        # 保存训练格式
        training_dir = self.output_path / "training_format"
        training_dir.mkdir(exist_ok=True)
        
        for split_name, split_data in training_splits.items():
            with open(training_dir / f"{split_name}.json", 'w', encoding='utf-8') as f:
                json.dump(split_data, f, ensure_ascii=False, indent=2)
        
        # 保存CSV
        csv_data.to_csv(self.output_path / "cribhd_text_dataset_final.csv", index=False, encoding='utf-8')
        
        # 保存统计信息
        with open(self.output_path / "final_statistics.json", 'w', encoding='utf-8') as f:
            json.dump(final_stats, f, ensure_ascii=False, indent=2)
        
        # 复制README
        if (self.dataset_path / "README.md").exists():
            shutil.copy2(self.dataset_path / "README.md", self.output_path / "README.md")
        
        print("✅ 最终数据集准备完成!")
        print(f"📁 输出目录: {self.output_path}")
        print(f"📊 最终统计:")
        print(f"   - 总样本数: {final_stats['数据集信息']['总样本数']:,}")
        print(f"   - 训练集: {final_stats['数据集信息']['训练集样本数']:,}")
        print(f"   - 验证集: {final_stats['数据集信息']['验证集样本数']:,}")
        print(f"   - 测试集: {final_stats['数据集信息']['测试集样本数']:,}")
        print(f"   - 平均文本长度: {final_stats['质量指标']['平均文本长度']} 字符")
        print(f"   - 完整样本比例: {final_stats['质量指标']['完整样本比例']}%")
        
        return final_stats

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CribHD数据集最终准备工具")
    parser.add_argument("--dataset_path", 
                       default="output/cribhd_text_dataset",
                       help="数据集路径")
    
    args = parser.parse_args()
    
    preparer = FinalDatasetPreparation(args.dataset_path)
    stats = preparer.prepare_final_dataset()
    
    return stats

if __name__ == "__main__":
    main()
