#!/usr/bin/env python3
"""
将CribHD文本数据集转换为instruction-input-output格式，适用于大模型微调训练
"""

import json
import os
from typing import Dict, List, Any
from datetime import datetime

class InstructionFormatConverter:
    def __init__(self):
        self.instruction_templates = {
            "scene_analysis": "请分析这张婴儿床场景图像，提供专业的婴幼儿发育评估和安全监测分析。",
            "development_assessment": "作为婴幼儿发育专家，请评估图像中婴儿的运动发育水平。",
            "safety_evaluation": "请从安全监护角度分析这张婴儿床场景，识别潜在风险并提供建议。",
            "parenting_guidance": "基于图像中的场景，为家长提供专业的育儿指导建议。",
            "comprehensive_report": "请提供这张婴儿床场景的综合分析报告，包括发育评估、安全分析和指导建议。"
        }
        
    def extract_scene_info(self, sample: Dict) -> str:
        """提取场景基本信息作为input"""
        scene_info = []
        
        # 添加年龄信息
        if sample.get('estimated_age_months'):
            scene_info.append(f"婴儿年龄: {sample['estimated_age_months']}个月")
            
        # 添加场景类型
        if sample.get('scene_type'):
            scene_info.append(f"场景类型: {sample['scene_type']}")
            
        # 添加发育水平
        if sample.get('development_level'):
            scene_info.append(f"发育水平: {sample['development_level']}")
            
        # 添加图像信息
        if sample.get('annotations', {}).get('image_info', {}).get('file_name'):
            scene_info.append(f"图像: {sample['annotations']['image_info']['file_name']}")
            
        return "场景信息: " + ", ".join(scene_info) if scene_info else "婴儿床场景图像"
    
    def create_instruction_samples(self, sample: Dict) -> List[Dict]:
        """为每个原始样本创建多个instruction格式的训练样本"""
        samples = []
        
        if not sample.get('raw_description') or not sample['raw_description']:
            return samples
            
        full_text = sample['raw_description'][0]['text']
        scene_input = self.extract_scene_info(sample)
        
        # 1. 完整场景分析任务
        samples.append({
            "instruction": self.instruction_templates["comprehensive_report"],
            "input": scene_input,
            "output": full_text
        })
        
        # 2. 提取各个部分作为专门的任务
        sections = self.parse_sections(full_text)
        
        if sections.get('基础场景描述'):
            samples.append({
                "instruction": "请描述婴儿床场景的基本情况，包括婴儿状态和环境物品。",
                "input": scene_input,
                "output": sections['基础场景描述']
            })
            
        if sections.get('运动发育评估'):
            samples.append({
                "instruction": self.instruction_templates["development_assessment"],
                "input": scene_input,
                "output": sections['运动发育评估']
            })
            
        if sections.get('安全风险评估'):
            samples.append({
                "instruction": self.instruction_templates["safety_evaluation"],
                "input": scene_input,
                "output": sections['安全风险评估']
            })
            
        if sections.get('育儿指导建议'):
            samples.append({
                "instruction": self.instruction_templates["parenting_guidance"],
                "input": scene_input,
                "output": sections['育儿指导建议']
            })
            
        if sections.get('专业总结'):
            samples.append({
                "instruction": "请对婴儿的整体发育状况进行专业总结。",
                "input": scene_input,
                "output": sections['专业总结']
            })
        
        return samples
    
    def parse_sections(self, text: str) -> Dict[str, str]:
        """解析文本中的各个部分"""
        sections = {}
        current_section = None
        current_content = []
        
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('## '):
                # 保存前一个部分
                if current_section and current_content:
                    sections[current_section] = '\n'.join(current_content).strip()
                
                # 开始新部分
                current_section = line.replace('## ', '').split('.', 1)[-1].strip()
                current_content = []
            elif line and current_section:
                current_content.append(line)
        
        # 保存最后一个部分
        if current_section and current_content:
            sections[current_section] = '\n'.join(current_content).strip()
            
        return sections
    
    def convert_dataset(self, input_file: str, output_dir: str):
        """转换整个数据集"""
        print(f"正在加载数据集: {input_file}")
        
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        all_samples = []
        processed_count = 0
        
        for sample in data['data']:
            instruction_samples = self.create_instruction_samples(sample)
            all_samples.extend(instruction_samples)
            processed_count += 1
            
            if processed_count % 100 == 0:
                print(f"已处理 {processed_count} 个样本，生成 {len(all_samples)} 个训练样本")
        
        print(f"转换完成！原始样本: {processed_count}, 生成训练样本: {len(all_samples)}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 保存完整的instruction格式数据集
        output_file = os.path.join(output_dir, 'cribhd_instruction_dataset.json')
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump({
                "metadata": {
                    "source": "CribHD婴幼儿家庭场景运动监测文本数据集",
                    "conversion_time": datetime.now().isoformat(),
                    "format": "instruction-input-output",
                    "original_samples": processed_count,
                    "instruction_samples": len(all_samples),
                    "tasks": list(self.instruction_templates.keys())
                },
                "data": all_samples
            }, f, ensure_ascii=False, indent=2)
        
        print(f"完整数据集已保存到: {output_file}")
        
        # 按8:1:1比例分割数据集
        self.split_dataset(all_samples, output_dir)
        
        # 生成统计信息
        self.generate_statistics(all_samples, output_dir)
    
    def split_dataset(self, samples: List[Dict], output_dir: str):
        """分割数据集为训练/验证/测试集"""
        import random
        random.seed(42)  # 确保可重现
        
        # 打乱数据
        shuffled_samples = samples.copy()
        random.shuffle(shuffled_samples)
        
        total = len(shuffled_samples)
        train_size = int(total * 0.8)
        valid_size = int(total * 0.1)
        
        train_data = shuffled_samples[:train_size]
        valid_data = shuffled_samples[train_size:train_size + valid_size]
        test_data = shuffled_samples[train_size + valid_size:]
        
        # 保存分割后的数据集
        splits = {
            'train': train_data,
            'valid': valid_data,
            'test': test_data
        }
        
        for split_name, split_data in splits.items():
            output_file = os.path.join(output_dir, f'{split_name}_instruction.json')
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(split_data, f, ensure_ascii=False, indent=2)
            print(f"{split_name}集已保存: {len(split_data)} 个样本 -> {output_file}")
    
    def generate_statistics(self, samples: List[Dict], output_dir: str):
        """生成数据集统计信息"""
        stats = {
            "总样本数": len(samples),
            "指令类型统计": {},
            "平均输入长度": 0,
            "平均输出长度": 0,
            "输入长度分布": {"最短": float('inf'), "最长": 0},
            "输出长度分布": {"最短": float('inf'), "最长": 0}
        }
        
        input_lengths = []
        output_lengths = []
        
        for sample in samples:
            instruction = sample['instruction']
            input_text = sample['input']
            output_text = sample['output']
            
            # 统计指令类型
            stats["指令类型统计"][instruction] = stats["指令类型统计"].get(instruction, 0) + 1
            
            # 统计长度
            input_len = len(input_text)
            output_len = len(output_text)
            
            input_lengths.append(input_len)
            output_lengths.append(output_len)
            
            stats["输入长度分布"]["最短"] = min(stats["输入长度分布"]["最短"], input_len)
            stats["输入长度分布"]["最长"] = max(stats["输入长度分布"]["最长"], input_len)
            stats["输出长度分布"]["最短"] = min(stats["输出长度分布"]["最短"], output_len)
            stats["输出长度分布"]["最长"] = max(stats["输出长度分布"]["最长"], output_len)
        
        stats["平均输入长度"] = sum(input_lengths) / len(input_lengths)
        stats["平均输出长度"] = sum(output_lengths) / len(output_lengths)
        
        # 保存统计信息
        stats_file = os.path.join(output_dir, 'instruction_dataset_statistics.json')
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"统计信息已保存到: {stats_file}")
        print(f"数据集统计:")
        print(f"  总样本数: {stats['总样本数']}")
        print(f"  平均输入长度: {stats['平均输入长度']:.1f} 字符")
        print(f"  平均输出长度: {stats['平均输出长度']:.1f} 字符")
        print(f"  指令类型数: {len(stats['指令类型统计'])}")

def main():
    converter = InstructionFormatConverter()
    
    # 输入和输出路径
    input_file = "output/cribhd_text_dataset/final_release/cribhd_text_dataset_final.json"
    output_dir = "output/cribhd_text_dataset/instruction_format"
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件不存在 {input_file}")
        return
    
    # 转换数据集
    converter.convert_dataset(input_file, output_dir)
    print("数据集转换完成！")

if __name__ == "__main__":
    main()
