#!/usr/bin/env python3
"""
增强型发育指导建议示例
演示如何使用规则基础 + LLM增强的混合策略
"""

import os
import json
from pathlib import Path
from cribnet_to_text_converter import CribNetTextConverter

def demonstrate_enhanced_guidance():
    """演示增强型指导建议生成"""
    print("=== 增强型发育指导建议演示 ===\n")
    
    # 测试场景
    test_scenarios = [
        {
            "name": "8个月婴儿玩具场景",
            "description": """
            8个月大的婴儿独立坐在婴儿床中，展示了良好的躯干控制能力。
            婴儿正在用双手抓握一个软质圆形小玩具，表现出钳形抓握的精细动作技能。
            该小型软质玩具靠近婴儿口部，存在潜在窒息风险。
            婴儿床内还有一条薄毯子部分覆盖婴儿腿部，位置适当未覆盖面部。
            """
        },
        {
            "name": "15个月学步儿场景",
            "description": """
            15个月大的幼儿在婴儿床中扶着栏杆站立，能够独立行走几步。
            床内有几个大小不同的积木玩具，幼儿正在尝试搭建。
            表现出良好的手眼协调能力和平衡感。
            """
        }
    ]
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    
    for scenario in test_scenarios:
        print(f"【{scenario['name']}】")
        print("=" * 50)
        print(f"场景描述: {scenario['description'].strip()}")
        print()
        
        # 方案1: 纯规则基础
        print("1. 纯规则基础建议:")
        print("-" * 30)
        converter_rule = CribNetTextConverter(model_type="local", use_llm_guidance=False)
        rule_guidance = converter_rule._generate_development_guidance(scenario['description'])
        
        print("适龄活动:")
        for activity in rule_guidance.get('age_appropriate_activities', []):
            print(f"  • {activity}")
        
        print("安全建议:")
        for safety in rule_guidance.get('safety_recommendations', []):
            print(f"  • {safety}")
        
        print()
        
        # 方案2: 规则基础 + LLM增强
        if api_key:
            print("2. 规则基础 + LLM增强建议:")
            print("-" * 30)
            converter_enhanced = CribNetTextConverter(
                model_type="qwen", 
                api_key=api_key, 
                use_llm_guidance=True
            )
            
            enhanced_guidance = converter_enhanced._generate_development_guidance(
                scenario['description'], use_llm=True
            )
            
            # 显示基础建议
            print("基础建议 (规则生成):")
            for activity in enhanced_guidance.get('age_appropriate_activities', []):
                print(f"  • {activity}")
            
            # 显示增强内容
            if 'enhanced_activities' in enhanced_guidance:
                print("\n详细实施指导 (LLM增强):")
                for enhanced_activity in enhanced_guidance.get('enhanced_activities', [])[:2]:  # 只显示前2个
                    print(f"  活动: {enhanced_activity.get('base_activity', 'N/A')}")
                    steps = enhanced_activity.get('detailed_steps', [])
                    if steps:
                        print(f"    步骤: {', '.join(steps[:2])}...")
                    tips = enhanced_activity.get('tips', [])
                    if tips:
                        print(f"    提示: {tips[0]}")
            
            if 'scene_specific_advice' in enhanced_guidance:
                advice = enhanced_guidance.get('scene_specific_advice', '')
                if advice:
                    print(f"\n个性化建议: {advice[:100]}...")
            
            print(f"\n生成方式: {enhanced_guidance.get('generation_method', 'N/A')}")
        else:
            print("2. 需要设置DASHSCOPE_API_KEY环境变量才能测试LLM增强功能")
        
        print("\n" + "="*70 + "\n")

def compare_guidance_quality():
    """对比指导建议的质量"""
    print("=== 指导建议质量对比 ===\n")
    
    comparison_aspects = [
        {
            "aspect": "内容丰富度",
            "rule_based": "标准化建议，覆盖基本需求",
            "llm_enhanced": "详细的实施步骤、注意事项、个性化调整"
        },
        {
            "aspect": "个性化程度", 
            "rule_based": "基于年龄段的通用建议",
            "llm_enhanced": "针对具体场景的定制化建议"
        },
        {
            "aspect": "专业准确性",
            "rule_based": "基于权威标准，准确性高",
            "llm_enhanced": "在规则基础上扩展，保持专业性"
        },
        {
            "aspect": "实用性",
            "rule_based": "简洁明了，易于理解",
            "llm_enhanced": "详细指导，包含具体操作方法"
        },
        {
            "aspect": "一致性",
            "rule_based": "高度一致，标准化",
            "llm_enhanced": "在保持核心一致性基础上增加变化"
        }
    ]
    
    print(f"{'对比维度':<12} {'规则基础':<25} {'LLM增强':<25}")
    print("-" * 70)
    
    for aspect in comparison_aspects:
        print(f"{aspect['aspect']:<12} {aspect['rule_based']:<25} {aspect['llm_enhanced']:<25}")
    
    print("\n推荐策略:")
    print("✓ 核心建议使用规则基础，确保专业性和一致性")
    print("✓ 使用LLM增强详细说明和个性化调整")
    print("✓ 在规则基础上扩展，而不是完全替代")
    print("✓ 适合需要高质量、个性化指导的应用场景")

def show_cost_benefit_analysis():
    """显示成本效益分析"""
    print("\n=== 成本效益分析 ===\n")
    
    print("方案对比:")
    print()
    
    print("【纯规则基础】")
    print("成本: 无API费用")
    print("速度: 极快 (<1ms)")
    print("质量: 标准化，专业准确")
    print("适用: 大规模批量处理")
    print()
    
    print("【纯LLM生成】")
    print("成本: 高API费用")
    print("速度: 较慢 (2-5秒)")
    print("质量: 个性化，但可能不一致")
    print("适用: 小规模精细化处理")
    print()
    
    print("【规则基础 + LLM增强】(推荐)")
    print("成本: 中等API费用 (仅增强部分)")
    print("速度: 中等 (1-3秒)")
    print("质量: 兼具标准化和个性化")
    print("适用: 平衡质量和成本的场景")
    print()
    
    print("成本估算 (假设1000张图像):")
    print("- 纯规则基础: ¥0")
    print("- 纯LLM生成: ¥200-500 (取决于模型)")
    print("- 规则+LLM增强: ¥50-150 (仅文本增强)")

def demonstrate_cribhd_processing():
    """演示CribHD数据集处理"""
    print("\n=== CribHD数据集处理演示 ===\n")
    
    # 检查数据集是否存在
    dataset_path = "./cribhd"
    if not os.path.exists(dataset_path):
        print(f"CribHD数据集不存在: {dataset_path}")
        print("请确保数据集已下载到cribhd文件夹")
        return
    
    print(f"发现CribHD数据集: {dataset_path}")
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("未设置DASHSCOPE_API_KEY，将使用规则基础方案")
        use_llm = False
    else:
        print(f"检测到API密钥: {api_key[:8]}...")
        use_llm = True
    
    # 创建转换器
    converter = CribNetTextConverter(
        model_type="qwen" if use_llm else "local",
        api_key=api_key,
        use_llm_guidance=use_llm
    )
    
    # 处理配置
    print("\n处理配置:")
    print(f"- 模型类型: {'Qwen + 规则增强' if use_llm else '纯规则基础'}")
    print(f"- 数据集路径: {dataset_path}")
    print(f"- 建议处理数量: 5-10张图像 (测试用)")
    
    # 询问是否继续
    if input("\n是否开始处理? (y/n): ").lower() == 'y':
        output_path = "./output/enhanced_cribhd_dataset"
        
        try:
            converter.convert_dataset(
                dataset_path=dataset_path,
                output_path=output_path,
                subset="cribhd_t",  # 先处理玩具子集
                max_images=5  # 限制数量用于测试
            )
            
            print(f"\n✓ 处理完成！结果保存在: {output_path}")
            
            # 显示结果示例
            show_sample_results(output_path)
            
        except Exception as e:
            print(f"处理失败: {str(e)}")
    else:
        print("已取消处理")

def show_sample_results(output_path):
    """显示示例结果"""
    json_file = Path(output_path) / "cribhd_text_dataset.json"
    if not json_file.exists():
        return
    
    print("\n=== 结果示例 ===")
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    if data.get("data"):
        sample = data["data"][0]
        
        print(f"图像ID: {sample.get('image_id', 'N/A')}")
        print(f"年龄估计: {sample.get('estimated_age_months', 0)}个月")
        print(f"生成方式: {sample.get('development_guidance', {}).get('generation_method', 'N/A')}")
        
        # 显示基础建议
        activities = sample.get('development_guidance', {}).get('age_appropriate_activities', [])
        if activities:
            print(f"基础活动建议: {activities[0]}")
        
        # 显示增强内容（如果有）
        enhanced = sample.get('development_guidance', {}).get('enhanced_activities', [])
        if enhanced:
            print(f"增强指导: 包含{len(enhanced)}项详细活动指导")
        
        scene_advice = sample.get('development_guidance', {}).get('scene_specific_advice', '')
        if scene_advice:
            print(f"个性化建议: {scene_advice[:80]}...")

def main():
    """主函数"""
    print("CribHD增强型发育指导建议生成工具")
    print("=" * 50)
    print("策略: 规则基础 + LLM增强")
    
    while True:
        print("\n请选择功能:")
        print("1. 演示增强型指导建议")
        print("2. 对比指导建议质量")
        print("3. 成本效益分析")
        print("4. 处理CribHD数据集")
        print("5. 退出")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            demonstrate_enhanced_guidance()
        elif choice == "2":
            compare_guidance_quality()
        elif choice == "3":
            show_cost_benefit_analysis()
        elif choice == "4":
            demonstrate_cribhd_processing()
        elif choice == "5":
            print("再见！")
            break
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
