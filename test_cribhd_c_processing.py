#!/usr/bin/env python3
"""
专门测试CRIBHD-C (模拟危机场景) 数据处理
验证120张危机场景图像的特殊处理逻辑
"""

import os
import json
from pathlib import Path
from cribnet_to_text_converter import CribNetTextConverter

def check_cribhd_c_structure():
    """检查CRIBHD-C的特殊结构"""
    print("=== 检查CRIBHD-C数据集结构 ===\n")
    
    cribhd_c_path = Path("./cribhd/CRIBHD-C")
    
    if not cribhd_c_path.exists():
        print(f"❌ CRIBHD-C目录不存在: {cribhd_c_path}")
        return False
    
    print(f"✓ 找到CRIBHD-C目录: {cribhd_c_path}")
    
    # 检查可能的图像路径
    possible_paths = [
        cribhd_c_path / "images",
        cribhd_c_path,
        cribhd_c_path / "data",
        cribhd_c_path / "crisis_scenes"
    ]
    
    total_images = 0
    found_path = None
    
    for path in possible_paths:
        if path.exists():
            # 检查多种图像格式
            image_files = []
            for ext in ["*.jpg", "*.jpeg", "*.png", "*.JPG", "*.JPEG", "*.PNG"]:
                image_files.extend(list(path.glob(ext)))
            
            if image_files:
                print(f"✓ 在 {path} 找到 {len(image_files)} 张图像")
                total_images += len(image_files)
                found_path = path
                
                # 显示一些示例文件名
                sample_files = sorted([f.name for f in image_files[:5]])
                print(f"  示例文件: {', '.join(sample_files)}")
                if len(image_files) > 5:
                    print(f"  ... 还有 {len(image_files) - 5} 张图像")
    
    if total_images == 0:
        print("❌ 未找到任何图像文件")
        print("检查的路径:")
        for path in possible_paths:
            print(f"  - {path}")
        return False
    
    # 验证图像数量
    if total_images == 120:
        print(f"✓ 图像数量正确: {total_images}/120")
    else:
        print(f"⚠️  图像数量异常: {total_images}/120 (预期120张)")
    
    # 检查注释文件
    annotation_files = [
        cribhd_c_path / "annotations.json",
        cribhd_c_path / "crisis_annotations.json", 
        cribhd_c_path / "metadata.json"
    ]
    
    found_annotations = False
    for ann_file in annotation_files:
        if ann_file.exists():
            print(f"✓ 找到注释文件: {ann_file}")
            found_annotations = True
            
            try:
                with open(ann_file, 'r', encoding='utf-8') as f:
                    ann_data = json.load(f)
                print(f"  注释文件包含 {len(ann_data)} 个顶级键")
                if isinstance(ann_data, dict):
                    for key in list(ann_data.keys())[:5]:
                        print(f"    - {key}")
            except Exception as e:
                print(f"  ⚠️  读取注释文件失败: {str(e)}")
    
    if not found_annotations:
        print("⚠️  未找到注释文件，将使用默认处理")
    
    return True

def test_cribhd_c_loading():
    """测试CRIBHD-C数据加载"""
    print("\n=== 测试CRIBHD-C数据加载 ===\n")
    
    converter = CribNetTextConverter(model_type="local")
    
    try:
        dataset_info = converter.load_cribhd_dataset("./cribhd")
        cribhd_c_data = dataset_info.get("cribhd_c", {})
        
        if not cribhd_c_data:
            print("❌ 未加载到CRIBHD-C数据")
            return False
        
        print("CRIBHD-C数据加载结果:")
        
        images = cribhd_c_data.get("images", [])
        metadata = cribhd_c_data.get("metadata", {})
        annotations = cribhd_c_data.get("annotations", {})
        
        print(f"  图像数量: {len(images)}")
        print(f"  元数据: {metadata.get('description', 'N/A')}")
        print(f"  特殊特征: {', '.join(metadata.get('special_features', []))}")
        print(f"  注释数据: {'有' if annotations else '无'}")
        
        if len(images) > 0:
            print(f"  第一张图像: {images[0].name}")
            print(f"  最后一张图像: {images[-1].name}")
        
        print("✓ CRIBHD-C数据加载成功")
        return True
        
    except Exception as e:
        print(f"❌ CRIBHD-C数据加载失败: {str(e)}")
        return False

def test_single_cribhd_c_processing():
    """测试单张CRIBHD-C图像处理"""
    print("\n=== 测试单张CRIBHD-C图像处理 ===\n")
    
    # 加载数据集找到第一张CRIBHD-C图像
    converter = CribNetTextConverter(model_type="local", use_llm_guidance=False)
    
    try:
        dataset_info = converter.load_cribhd_dataset("./cribhd")
        cribhd_c_data = dataset_info.get("cribhd_c", {})
        images = cribhd_c_data.get("images", [])
        
        if not images:
            print("❌ 未找到CRIBHD-C图像")
            return False
        
        test_image = images[0]
        print(f"测试图像: {test_image}")
        
        # 准备CRIBHD-C特殊注释
        metadata = cribhd_c_data.get("metadata", {})
        annotations = cribhd_c_data.get("annotations", {})
        
        cribhd_c_annotations = converter._prepare_cribhd_c_annotations(
            test_image.name, annotations, metadata
        )
        
        print("CRIBHD-C特殊注释信息:")
        print(f"  数据集类型: {cribhd_c_annotations.get('dataset_type')}")
        print(f"  场景类型: {cribhd_c_annotations.get('scene_type')}")
        print(f"  风险等级: {cribhd_c_annotations.get('risk_level')}")
        
        focus_areas = cribhd_c_annotations.get('special_instructions', {}).get('focus_areas', [])
        if focus_areas:
            print("  分析重点:")
            for area in focus_areas:
                print(f"    - {area}")
        
        # 生成文本描述
        print("\n生成文本描述...")
        description = converter.generate_text_description(str(test_image), cribhd_c_annotations)
        
        print("\n处理结果:")
        print(f"  图像ID: {description.get('image_id')}")
        print(f"  子集类型: {description.get('subset_type', 'N/A')}")
        print(f"  风险等级: {description.get('risk_assessment', {}).get('risk_level', 'N/A')}")
        print(f"  年龄估计: {description.get('estimated_age_months', 0)}个月")
        
        # 显示原始描述的前100个字符
        raw_desc = description.get('raw_description', '')
        if raw_desc:
            print(f"  描述预览: {raw_desc[:100]}...")
        
        # 检查是否识别为危机场景
        risks = description.get('risk_assessment', {}).get('specific_risks', [])
        if risks:
            print(f"  识别风险: {', '.join(risks[:3])}")
        
        print("✓ 单张CRIBHD-C图像处理成功")
        return True
        
    except Exception as e:
        print(f"❌ 单张CRIBHD-C图像处理失败: {str(e)}")
        return False

def test_cribhd_c_batch_processing():
    """测试CRIBHD-C批量处理"""
    print("\n=== 测试CRIBHD-C批量处理 ===\n")
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    
    if api_key:
        print("检测到API密钥，使用Qwen增强模式")
        converter = CribNetTextConverter(
            model_type="qwen",
            api_key=api_key,
            use_llm_guidance=True
        )
        mode = "Qwen增强"
    else:
        print("未检测到API密钥，使用规则基础模式")
        converter = CribNetTextConverter(model_type="local", use_llm_guidance=False)
        mode = "规则基础"
    
    output_path = "./output/test_cribhd_c_processing"
    
    print(f"\n处理配置:")
    print(f"  模式: {mode}")
    print(f"  子集: CRIBHD-C (危机场景)")
    print(f"  输出: {output_path}")
    print(f"  限制: 5张图像 (测试用)")
    
    if input("\n是否开始CRIBHD-C批量处理测试? (y/n): ").lower() != 'y':
        print("已取消测试")
        return False
    
    try:
        converter.convert_dataset(
            dataset_path="./cribhd",
            output_path=output_path,
            subset="cribhd_c",  # 专门处理CRIBHD-C
            max_images=5
        )
        
        print(f"\n✓ CRIBHD-C批量处理测试成功！")
        
        # 分析结果
        json_file = Path(output_path) / "cribhd_text_dataset.json"
        if json_file.exists():
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print("\n处理结果分析:")
            total_images = len(data.get("data", []))
            print(f"  处理图像数: {total_images}")
            
            # 检查CRIBHD-C特有信息
            cribhd_c_info = data.get("metadata", {}).get("cribhd_c_info", {})
            if cribhd_c_info:
                print(f"  数据集描述: {cribhd_c_info.get('description')}")
                print(f"  风险等级: {cribhd_c_info.get('risk_level')}")
                print(f"  特殊说明: {cribhd_c_info.get('special_notes')}")
            
            # 分析第一个样本
            if data.get("data"):
                sample = data["data"][0]
                print(f"\n样本分析:")
                print(f"  图像ID: {sample.get('image_id')}")
                print(f"  子集类型: {sample.get('subset_type')}")
                print(f"  场景类别: {sample.get('scene_category')}")
                print(f"  风险等级: {sample.get('hazard_category')}")
                
                expected_risks = sample.get('expected_risks', [])
                if expected_risks:
                    print(f"  预期风险: {', '.join(expected_risks[:3])}")
                
                guidance_method = sample.get('development_guidance', {}).get('generation_method', 'N/A')
                print(f"  指导生成方式: {guidance_method}")
        
        return True
        
    except Exception as e:
        print(f"❌ CRIBHD-C批量处理测试失败: {str(e)}")
        return False

def show_cribhd_c_features():
    """展示CRIBHD-C的特殊功能"""
    print("\n=== CRIBHD-C特殊功能说明 ===\n")
    
    print("CRIBHD-C (模拟危机场景) 特点:")
    print("✓ 120张高风险模拟场景图像")
    print("✓ 使用仿真玩偶和道具构建")
    print("✓ 专门训练危机识别能力")
    print("✓ 包含面部遮挡、四肢受限等场景")
    
    print("\n特殊处理功能:")
    print("✓ 自动检测多种可能的图像路径")
    print("✓ 支持多种图像格式 (jpg, jpeg, png)")
    print("✓ 专门的危机场景注释处理")
    print("✓ 高风险场景的特殊提示词")
    print("✓ 针对性的安全风险评估")
    
    print("\n与其他子集的区别:")
    print("- CRIBHD-T/B: COCO格式，有详细标注")
    print("- CRIBHD-C: 特殊结构，重点在危机识别")
    print("- 风险等级: CRIBHD-C默认为高风险")
    print("- 分析重点: 安全风险 > 发育评估")

def main():
    """主函数"""
    print("CRIBHD-C (模拟危机场景) 专项测试工具")
    print("=" * 50)
    
    tests = [
        ("CRIBHD-C结构检查", check_cribhd_c_structure),
        ("CRIBHD-C数据加载测试", test_cribhd_c_loading),
        ("单张图像处理测试", test_single_cribhd_c_processing),
        ("批量处理测试", test_cribhd_c_batch_processing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"执行测试: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if not result:
                print(f"\n⚠️  测试 '{test_name}' 未通过")
                if input("继续下一个测试? (y/n): ").lower() != 'y':
                    break
        except Exception as e:
            print(f"\n❌ 测试 '{test_name}' 出现异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示特殊功能说明
    show_cribhd_c_features()
    
    # 测试总结
    print(f"\n{'='*60}")
    print("CRIBHD-C测试总结")
    print('='*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name:<30} {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 CRIBHD-C处理功能完全正常！")
        print("\n可以开始处理完整的CRIBHD-C数据集了。")
    else:
        print("⚠️  部分功能需要检查，请根据错误信息进行调整。")

if __name__ == "__main__":
    main()
