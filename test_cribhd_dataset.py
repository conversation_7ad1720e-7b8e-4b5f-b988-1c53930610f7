#!/usr/bin/env python3
"""
测试CribHD数据集结构和处理
验证数据集是否正确加载，并演示增强型指导建议生成
"""

import os
import json
from pathlib import Path
from cribnet_to_text_converter import CribNetTextConverter

def check_cribhd_structure():
    """检查CribHD数据集结构"""
    print("=== 检查CribHD数据集结构 ===\n")
    
    dataset_path = Path("./cribhd")
    
    if not dataset_path.exists():
        print(f"❌ 数据集目录不存在: {dataset_path}")
        print("请确保CribHD数据集已下载到cribhd文件夹")
        return False
    
    print(f"✓ 找到数据集目录: {dataset_path}")
    
    # 检查子集目录
    expected_subsets = ["CRIBHD-T", "CRIBHD-B", "CRIBHD-C"]
    found_subsets = []
    
    for subset in expected_subsets:
        subset_path = dataset_path / subset
        if subset_path.exists():
            found_subsets.append(subset)
            print(f"✓ 找到子集: {subset}")
            
            # 检查COCO格式数据
            coco_path = subset_path / "COCO"
            if coco_path.exists():
                print(f"  ✓ COCO格式数据: {coco_path}")
                
                # 检查train/test/valid分割
                for split in ["train", "test", "valid"]:
                    split_path = coco_path / split
                    if split_path.exists():
                        images_path = split_path / "images"
                        ann_file = split_path / "_annotations.coco.json"
                        
                        image_count = len(list(images_path.glob("*.jpg"))) if images_path.exists() else 0
                        ann_exists = ann_file.exists()
                        
                        print(f"    {split}: {image_count} 图像, 注释文件: {'✓' if ann_exists else '❌'}")
            else:
                print(f"  ❌ 缺少COCO格式数据")
        else:
            print(f"❌ 缺少子集: {subset}")
    
    # 特殊处理CRIBHD-C
    cribhd_c_images = dataset_path / "CRIBHD-C" / "images"
    if cribhd_c_images.exists():
        c_image_count = len(list(cribhd_c_images.glob("*.jpg")))
        print(f"  CRIBHD-C直接图像: {c_image_count} 张")
    
    if found_subsets:
        print(f"\n✓ 数据集结构检查完成，找到 {len(found_subsets)} 个子集")
        return True
    else:
        print("\n❌ 未找到有效的数据集子集")
        return False

def test_data_loading():
    """测试数据加载功能"""
    print("\n=== 测试数据加载功能 ===\n")
    
    converter = CribNetTextConverter(model_type="local")
    
    try:
        dataset_info = converter.load_cribhd_dataset("./cribhd")
        
        print("数据集加载结果:")
        for subset_name, subset_data in dataset_info.items():
            print(f"\n{subset_name}:")
            
            if subset_name in ["cribhd_b", "cribhd_t"]:
                # COCO格式数据
                for split in ["train", "test", "valid"]:
                    if split in subset_data and subset_data[split]:
                        images = subset_data[split].get("images", [])
                        annotations = subset_data[split].get("annotations", {})
                        
                        image_count = len(images)
                        ann_count = len(annotations.get("annotations", [])) if annotations else 0
                        
                        print(f"  {split}: {image_count} 图像, {ann_count} 注释")
            
            elif subset_name == "cribhd_c":
                # 直接图像数据
                images = subset_data.get("images", [])
                print(f"  images: {len(images)} 张")
        
        print("\n✓ 数据加载测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 数据加载失败: {str(e)}")
        return False

def test_single_image_processing():
    """测试单张图像处理"""
    print("\n=== 测试单张图像处理 ===\n")
    
    # 查找第一张可用图像
    dataset_path = Path("./cribhd")
    test_image = None
    
    # 搜索顺序：CRIBHD-T -> CRIBHD-B -> CRIBHD-C
    search_paths = [
        dataset_path / "CRIBHD-T" / "COCO" / "train" / "images",
        dataset_path / "CRIBHD-T" / "COCO" / "test" / "images",
        dataset_path / "CRIBHD-B" / "COCO" / "train" / "images",
        dataset_path / "CRIBHD-C" / "images"
    ]
    
    for search_path in search_paths:
        if search_path.exists():
            images = list(search_path.glob("*.jpg"))
            if images:
                test_image = images[0]
                break
    
    if not test_image:
        print("❌ 未找到可用的测试图像")
        return False
    
    print(f"使用测试图像: {test_image}")
    
    # 测试规则基础处理
    print("\n1. 规则基础处理:")
    print("-" * 30)
    
    converter_rule = CribNetTextConverter(model_type="local", use_llm_guidance=False)
    
    try:
        result_rule = converter_rule.generate_text_description(str(test_image))
        
        print(f"年龄估计: {result_rule.get('estimated_age_months', 0)}个月")
        print(f"运动技能: {', '.join(result_rule.get('motor_skills', []))}")
        print(f"风险等级: {result_rule.get('risk_assessment', {}).get('risk_level', 'N/A')}")
        
        guidance = result_rule.get('development_guidance', {})
        activities = guidance.get('age_appropriate_activities', [])
        if activities:
            print(f"活动建议: {activities[0]}")
        
        print(f"生成方式: {guidance.get('generation_method', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 规则基础处理失败: {str(e)}")
        return False
    
    # 测试LLM增强处理（如果有API密钥）
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if api_key:
        print("\n2. LLM增强处理:")
        print("-" * 30)
        
        converter_enhanced = CribNetTextConverter(
            model_type="qwen", 
            api_key=api_key, 
            use_llm_guidance=True
        )
        
        try:
            result_enhanced = converter_enhanced.generate_text_description(str(test_image))
            
            guidance_enhanced = result_enhanced.get('development_guidance', {})
            
            print(f"生成方式: {guidance_enhanced.get('generation_method', 'N/A')}")
            
            # 显示增强内容
            enhanced_activities = guidance_enhanced.get('enhanced_activities', [])
            if enhanced_activities:
                print(f"详细指导: 包含{len(enhanced_activities)}项增强活动")
                
                # 显示第一项的详细信息
                first_activity = enhanced_activities[0]
                print(f"  活动: {first_activity.get('base_activity', 'N/A')}")
                steps = first_activity.get('detailed_steps', [])
                if steps:
                    print(f"  步骤: {steps[0]}")
            
            scene_advice = guidance_enhanced.get('scene_specific_advice', '')
            if scene_advice:
                print(f"个性化建议: {scene_advice[:100]}...")
            
        except Exception as e:
            print(f"❌ LLM增强处理失败: {str(e)}")
    else:
        print("\n2. LLM增强处理:")
        print("-" * 30)
        print("需要设置DASHSCOPE_API_KEY环境变量")
    
    print("\n✓ 单张图像处理测试完成")
    return True

def test_batch_processing():
    """测试批量处理"""
    print("\n=== 测试批量处理 ===\n")
    
    api_key = os.getenv("DASHSCOPE_API_KEY")
    
    # 选择处理方式
    if api_key:
        print("检测到API密钥，使用LLM增强模式")
        converter = CribNetTextConverter(
            model_type="qwen",
            api_key=api_key,
            use_llm_guidance=True
        )
        mode_desc = "LLM增强"
    else:
        print("未检测到API密钥，使用规则基础模式")
        converter = CribNetTextConverter(model_type="local", use_llm_guidance=False)
        mode_desc = "规则基础"
    
    # 处理配置
    dataset_path = "./cribhd"
    output_path = "./output/test_cribhd_processing"
    
    print(f"\n处理配置:")
    print(f"- 模式: {mode_desc}")
    print(f"- 数据集: {dataset_path}")
    print(f"- 输出: {output_path}")
    print(f"- 子集: CRIBHD-T (玩具)")
    print(f"- 限制: 3张图像 (测试用)")
    
    # 询问是否继续
    if input("\n是否开始批量处理测试? (y/n): ").lower() != 'y':
        print("已取消测试")
        return False
    
    try:
        converter.convert_dataset(
            dataset_path=dataset_path,
            output_path=output_path,
            subset="cribhd_t",
            max_images=3
        )
        
        print(f"\n✓ 批量处理测试成功！")
        
        # 显示结果摘要
        json_file = Path(output_path) / "cribhd_text_dataset.json"
        if json_file.exists():
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            total_images = len(data.get("data", []))
            print(f"处理图像数: {total_images}")
            
            if data.get("data"):
                sample = data["data"][0]
                guidance_method = sample.get('development_guidance', {}).get('generation_method', 'N/A')
                print(f"指导生成方式: {guidance_method}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批量处理测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("CribHD数据集测试工具")
    print("=" * 40)
    print("测试增强型发育指导建议生成功能")
    
    # 执行测试序列
    tests = [
        ("数据集结构检查", check_cribhd_structure),
        ("数据加载测试", test_data_loading),
        ("单张图像处理测试", test_single_image_processing),
        ("批量处理测试", test_batch_processing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"执行测试: {test_name}")
        print('='*60)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if not result:
                print(f"\n⚠️  测试 '{test_name}' 未通过，是否继续？")
                if input("继续下一个测试? (y/n): ").lower() != 'y':
                    break
        except Exception as e:
            print(f"\n❌ 测试 '{test_name}' 出现异常: {str(e)}")
            results.append((test_name, False))
    
    # 显示测试总结
    print(f"\n{'='*60}")
    print("测试总结")
    print('='*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✓ 通过" if result else "❌ 失败"
        print(f"{test_name:<25} {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！您的CribHD数据集已准备就绪。")
        print("\n下一步建议:")
        print("1. 运行 python enhanced_guidance_example.py 查看详细功能演示")
        print("2. 使用 python cribnet_to_text_converter.py 进行正式处理")
        print("3. 根据需要调整处理参数和输出格式")
    else:
        print("⚠️  部分测试未通过，请检查数据集结构和配置。")

if __name__ == "__main__":
    main()
