#!/usr/bin/env python3
"""
CribHD数据集质量验证工具
检查数据完整性、一致性和质量问题
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Tuple
import re
from collections import defaultdict

class DatasetValidator:
    """数据集验证器"""
    
    def __init__(self, dataset_path: str):
        self.dataset_path = Path(dataset_path)
        self.issues = defaultdict(list)
        
    def load_all_formats(self) -> Dict[str, Any]:
        """加载所有格式的数据"""
        data = {}
        
        # 加载主数据集
        main_file = self.dataset_path / "cribhd_text_dataset.json"
        if main_file.exists():
            with open(main_file, 'r', encoding='utf-8') as f:
                data['main'] = json.load(f)
        
        # 加载训练格式
        train_file = self.dataset_path / "training_format" / "train.json"
        valid_file = self.dataset_path / "training_format" / "valid.json"
        test_file = self.dataset_path / "training_format" / "test.json"
        
        if train_file.exists():
            with open(train_file, 'r', encoding='utf-8') as f:
                data['train'] = json.load(f)
        if valid_file.exists():
            with open(valid_file, 'r', encoding='utf-8') as f:
                data['valid'] = json.load(f)
        if test_file.exists():
            with open(test_file, 'r', encoding='utf-8') as f:
                data['test'] = json.load(f)
        
        # 加载CSV
        csv_file = self.dataset_path / "cribhd_text_dataset.csv"
        if csv_file.exists():
            data['csv'] = pd.read_csv(csv_file)
            
        return data
    
    def validate_data_completeness(self, data: Dict[str, Any]) -> List[str]:
        """验证数据完整性"""
        issues = []
        
        # 检查主数据集
        if 'main' not in data:
            issues.append("❌ 主数据集文件缺失")
            return issues
            
        main_data = data['main'].get('data', [])
        if not main_data:
            issues.append("❌ 主数据集为空")
            return issues
            
        # 检查训练格式
        train_count = len(data.get('train', []))
        valid_count = len(data.get('valid', []))
        test_count = len(data.get('test', []))
        total_training = train_count + valid_count + test_count
        
        if total_training == 0:
            issues.append("❌ 训练格式数据缺失")
        elif abs(len(main_data) - total_training) > 200:  # 允许一定差异
            issues.append(f"⚠️ 数据量不匹配: 主数据集{len(main_data)}条，训练格式{total_training}条")
        
        # 检查CSV格式
        if 'csv' in data:
            csv_count = len(data['csv'])
            if abs(len(main_data) - csv_count) > 10:
                issues.append(f"⚠️ CSV数据量不匹配: 主数据集{len(main_data)}条，CSV{csv_count}条")
        else:
            issues.append("⚠️ CSV格式文件缺失")
            
        return issues
    
    def validate_data_structure(self, data: Dict[str, Any]) -> List[str]:
        """验证数据结构"""
        issues = []
        
        if 'main' not in data:
            return ["❌ 无法验证数据结构：主数据集缺失"]
            
        main_data = data['main'].get('data', [])
        
        # 检查必需字段
        required_fields = [
            'raw_description', 'estimated_age_months', 'development_level'
        ]
        
        missing_fields = defaultdict(int)
        invalid_types = defaultdict(int)
        
        for i, item in enumerate(main_data[:100]):  # 检查前100个样本
            for field in required_fields:
                if field not in item:
                    missing_fields[field] += 1
                elif field == 'estimated_age_months' and not isinstance(item[field], (int, float)):
                    invalid_types[field] += 1
                elif field == 'raw_description' and not isinstance(item[field], list):
                    invalid_types[field] += 1
        
        for field, count in missing_fields.items():
            if count > 0:
                issues.append(f"❌ 字段'{field}'在{count}个样本中缺失")
                
        for field, count in invalid_types.items():
            if count > 0:
                issues.append(f"❌ 字段'{field}'在{count}个样本中类型错误")
        
        return issues
    
    def validate_text_quality(self, data: Dict[str, Any]) -> List[str]:
        """验证文本质量"""
        issues = []
        
        if 'main' not in data:
            return ["❌ 无法验证文本质量：主数据集缺失"]
            
        main_data = data['main'].get('data', [])
        
        # 文本质量指标
        empty_texts = 0
        short_texts = 0
        missing_sections = 0
        
        required_sections = [
            "基础场景描述", "运动发育评估", "安全风险评估", 
            "育儿指导建议", "专业总结"
        ]
        
        for item in main_data[:200]:  # 检查前200个样本
            raw_desc = item.get('raw_description', [])
            if not raw_desc or not isinstance(raw_desc, list):
                empty_texts += 1
                continue
                
            text = raw_desc[0].get('text', '') if raw_desc else ''
            
            if len(text) < 100:
                short_texts += 1
                
            # 检查必需章节
            missing_count = 0
            for section in required_sections:
                if section not in text:
                    missing_count += 1
                    
            if missing_count > 2:  # 允许缺失2个章节
                missing_sections += 1
        
        if empty_texts > 0:
            issues.append(f"❌ {empty_texts}个样本的文本为空")
        if short_texts > 10:
            issues.append(f"⚠️ {short_texts}个样本的文本过短(<100字符)")
        if missing_sections > 20:
            issues.append(f"⚠️ {missing_sections}个样本缺失重要章节")
            
        return issues
    
    def validate_label_consistency(self, data: Dict[str, Any]) -> List[str]:
        """验证标签一致性"""
        issues = []
        
        # 检查年龄标签的合理性
        if 'main' in data:
            main_data = data['main'].get('data', [])
            age_issues = 0
            
            for item in main_data[:100]:
                age = item.get('estimated_age_months', 0)
                if age < 0 or age > 36:  # 婴幼儿年龄范围
                    age_issues += 1
                    
            if age_issues > 0:
                issues.append(f"⚠️ {age_issues}个样本的年龄标签异常")
        
        # 检查训练格式标签
        if 'train' in data:
            train_data = data['train']
            label_issues = 0
            
            for item in train_data[:50]:
                labels = item.get('labels', {})
                if 'age_months' not in labels:
                    label_issues += 1
                elif not isinstance(labels['age_months'], (int, float)):
                    label_issues += 1
                    
            if label_issues > 0:
                issues.append(f"❌ 训练格式中{label_issues}个样本的标签有问题")
                
        return issues
    
    def generate_quality_report(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """生成质量报告"""
        report = {
            "验证时间": pd.Timestamp.now().isoformat(),
            "数据集概况": {},
            "质量问题": {},
            "建议改进": []
        }
        
        # 数据集概况
        if 'main' in data:
            main_data = data['main'].get('data', [])
            report["数据集概况"] = {
                "主数据集样本数": len(main_data),
                "训练集样本数": len(data.get('train', [])),
                "验证集样本数": len(data.get('valid', [])),
                "测试集样本数": len(data.get('test', [])),
                "CSV记录数": len(data.get('csv', [])) if 'csv' in data else 0
            }
        
        # 收集所有问题
        all_issues = []
        all_issues.extend(self.validate_data_completeness(data))
        all_issues.extend(self.validate_data_structure(data))
        all_issues.extend(self.validate_text_quality(data))
        all_issues.extend(self.validate_label_consistency(data))
        
        # 分类问题
        critical_issues = [issue for issue in all_issues if issue.startswith("❌")]
        warning_issues = [issue for issue in all_issues if issue.startswith("⚠️")]
        
        report["质量问题"] = {
            "严重问题": critical_issues,
            "警告问题": warning_issues,
            "问题总数": len(all_issues)
        }
        
        # 生成改进建议
        if critical_issues:
            report["建议改进"].append("🔧 优先解决严重问题，确保数据完整性")
        if warning_issues:
            report["建议改进"].append("⚡ 关注警告问题，提升数据质量")
        if not all_issues:
            report["建议改进"].append("✅ 数据质量良好，可以正常使用")
            
        return report
    
    def validate_dataset(self) -> Dict[str, Any]:
        """执行完整的数据集验证"""
        print("🔍 开始验证数据集...")
        
        # 加载所有格式数据
        print("📂 加载数据文件...")
        data = self.load_all_formats()
        
        # 生成质量报告
        print("📊 生成质量报告...")
        report = self.generate_quality_report(data)
        
        # 保存报告
        report_file = self.dataset_path / "quality_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✅ 验证完成!")
        print(f"📋 质量报告已保存: {report_file}")
        
        # 打印摘要
        print(f"\n📊 验证摘要:")
        print(f"严重问题: {len(report['质量问题']['严重问题'])}个")
        print(f"警告问题: {len(report['质量问题']['警告问题'])}个")
        
        if report['质量问题']['严重问题']:
            print("\n❌ 严重问题:")
            for issue in report['质量问题']['严重问题']:
                print(f"  {issue}")
                
        if report['质量问题']['警告问题']:
            print("\n⚠️ 警告问题:")
            for issue in report['质量问题']['警告问题'][:5]:  # 只显示前5个
                print(f"  {issue}")
            if len(report['质量问题']['警告问题']) > 5:
                print(f"  ... 还有{len(report['质量问题']['警告问题']) - 5}个警告")
        
        return report

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="CribHD数据集质量验证工具")
    parser.add_argument("--dataset_path", 
                       default="output/cribhd_text_dataset",
                       help="数据集路径")
    
    args = parser.parse_args()
    
    validator = DatasetValidator(args.dataset_path)
    report = validator.validate_dataset()
    
    # 返回验证结果
    return len(report['质量问题']['严重问题']) == 0

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
