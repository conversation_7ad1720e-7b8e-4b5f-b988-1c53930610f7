#!/usr/bin/env python3
"""
发育指导建议生成方式对比示例
演示规则基础 vs LLM生成的区别
"""

import os
import json
from cribnet_to_text_converter import CribNetTextConverter

def compare_guidance_methods():
    """对比两种发育指导建议生成方式"""
    print("=== 发育指导建议生成方式对比 ===\n")
    
    # 模拟一个图像描述
    sample_description = """
    图像中8个月大的婴儿独立坐在婴儿床中，展示了良好的躯干控制能力。
    婴儿正在用双手抓握一个软质圆形小玩具，表现出钳形抓握的精细动作技能。
    然而，该小型软质玩具靠近婴儿口部，存在潜在窒息风险。
    婴儿床内还有一条薄毯子部分覆盖婴儿腿部，位置适当未覆盖面部。
    光线充足，婴儿表情专注，正在探索玩具的质地。
    """
    
    print("测试场景描述:")
    print("-" * 50)
    print(sample_description)
    print("-" * 50)
    
    # 方案1：规则基础
    print("\n【方案1：规则基础发育指导】")
    print("=" * 40)
    
    converter_rule = CribNetTextConverter(
        model_type="local", 
        use_llm_guidance=False
    )
    
    rule_guidance = converter_rule._generate_development_guidance(sample_description)
    print(json.dumps(rule_guidance, ensure_ascii=False, indent=2))
    
    # 方案2：LLM生成（如果有API密钥）
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if api_key:
        print("\n【方案2：LLM生成发育指导】")
        print("=" * 40)
        
        converter_llm = CribNetTextConverter(
            model_type="qwen", 
            api_key=api_key,
            use_llm_guidance=True
        )
        
        llm_guidance = converter_llm._generate_development_guidance(sample_description, use_llm=True)
        print(json.dumps(llm_guidance, ensure_ascii=False, indent=2))
        
        # 对比分析
        print("\n【对比分析】")
        print("=" * 40)
        analyze_guidance_differences(rule_guidance, llm_guidance)
    else:
        print("\n【方案2：LLM生成】")
        print("需要设置DASHSCOPE_API_KEY环境变量才能测试LLM生成")

def analyze_guidance_differences(rule_guidance, llm_guidance):
    """分析两种方案的差异"""
    
    print("1. 生成方式:")
    print(f"   规则基础: {rule_guidance.get('generation_method', 'N/A')}")
    print(f"   LLM生成: {llm_guidance.get('generation_method', 'N/A')}")
    
    print("\n2. 适龄活动建议数量:")
    rule_activities = len(rule_guidance.get('age_appropriate_activities', []))
    llm_activities = len(llm_guidance.get('age_appropriate_activities', []))
    print(f"   规则基础: {rule_activities} 条")
    print(f"   LLM生成: {llm_activities} 条")
    
    print("\n3. 安全建议数量:")
    rule_safety = len(rule_guidance.get('safety_recommendations', []))
    llm_safety = len(llm_guidance.get('safety_recommendations', []))
    print(f"   规则基础: {rule_safety} 条")
    print(f"   LLM生成: {llm_safety} 条")
    
    print("\n4. 个性化程度:")
    if 'personalized_notes' in llm_guidance:
        print("   规则基础: 标准化建议")
        print("   LLM生成: 包含个性化建议")
    else:
        print("   两种方案都是标准化建议")
    
    print("\n5. 内容差异分析:")
    
    # 分析活动建议的差异
    rule_act_set = set(rule_guidance.get('age_appropriate_activities', []))
    llm_act_set = set(llm_guidance.get('age_appropriate_activities', []))
    
    common_activities = rule_act_set & llm_act_set
    rule_only = rule_act_set - llm_act_set
    llm_only = llm_act_set - rule_act_set
    
    if common_activities:
        print(f"   共同建议: {len(common_activities)} 条")
    if rule_only:
        print(f"   规则独有: {len(rule_only)} 条")
    if llm_only:
        print(f"   LLM独有: {len(llm_only)} 条")

def test_different_scenarios():
    """测试不同场景下的指导建议"""
    print("\n=== 不同场景测试 ===\n")
    
    scenarios = [
        {
            "name": "新生儿场景",
            "description": "2个月大的新生儿仰卧在婴儿床中，头部能够短暂抬起，对声音有反应。"
        },
        {
            "name": "学步期场景", 
            "description": "15个月大的幼儿在婴儿床中扶着栏杆站立，能够独立行走几步。"
        },
        {
            "name": "高风险场景",
            "description": "10个月大的婴儿趴在床上，面前有多个小玩具，其中一个直径约2厘米的小球靠近口部。"
        }
    ]
    
    converter = CribNetTextConverter(model_type="local", use_llm_guidance=False)
    
    for scenario in scenarios:
        print(f"【{scenario['name']}】")
        print(f"描述: {scenario['description']}")
        
        guidance = converter._generate_development_guidance(scenario['description'])
        
        print("建议摘要:")
        activities = guidance.get('age_appropriate_activities', [])
        safety = guidance.get('safety_recommendations', [])
        milestones = guidance.get('next_milestones', [])
        
        if activities:
            print(f"  适龄活动: {activities[0]}")
        if safety:
            print(f"  安全建议: {safety[0]}")
        if milestones:
            print(f"  下阶段目标: {', '.join(milestones[:2])}")
        
        print("-" * 50)

def demonstrate_api_cost_consideration():
    """演示API成本考虑"""
    print("\n=== API成本考虑 ===\n")
    
    print("规则基础方案:")
    print("✓ 无API调用成本")
    print("✓ 响应速度快 (<1ms)")
    print("✓ 可离线运行")
    print("✓ 结果一致性高")
    
    print("\nLLM生成方案:")
    print("⚠️ 每次调用产生API费用")
    print("⚠️ 响应时间较长 (1-3秒)")
    print("⚠️ 需要网络连接")
    print("✓ 个性化程度高")
    print("✓ 语言更自然")
    
    print("\n推荐策略:")
    print("1. 开发阶段：使用规则基础，快速迭代")
    print("2. 小规模测试：使用LLM生成，评估效果")
    print("3. 生产环境：根据预算和需求选择")
    print("4. 混合方案：核心建议用规则，个性化补充用LLM")

def show_configuration_options():
    """展示配置选项"""
    print("\n=== 配置选项 ===\n")
    
    print("在代码中的使用方式:")
    print()
    print("# 方案1：仅使用规则基础")
    print("converter = CribNetTextConverter(")
    print("    model_type='qwen',")
    print("    api_key='your_key',")
    print("    use_llm_guidance=False  # 默认值")
    print(")")
    print()
    print("# 方案2：使用LLM生成指导建议")
    print("converter = CribNetTextConverter(")
    print("    model_type='qwen',")
    print("    api_key='your_key',")
    print("    use_llm_guidance=True")
    print(")")
    print()
    print("# 方案3：运行时动态选择")
    print("converter = CribNetTextConverter(model_type='qwen', api_key='your_key')")
    print("# 对重要场景使用LLM")
    print("guidance = converter._generate_development_guidance(description, use_llm=True)")
    print("# 对一般场景使用规则")
    print("guidance = converter._generate_development_guidance(description, use_llm=False)")

def main():
    """主函数"""
    print("CribHD发育指导建议生成方式对比工具")
    print("=" * 50)
    
    while True:
        print("\n请选择测试项目:")
        print("1. 对比两种生成方式")
        print("2. 测试不同场景")
        print("3. API成本考虑说明")
        print("4. 配置选项说明")
        print("5. 退出")
        
        choice = input("请选择 (1-5): ").strip()
        
        if choice == "1":
            compare_guidance_methods()
        elif choice == "2":
            test_different_scenarios()
        elif choice == "3":
            demonstrate_api_cost_consideration()
        elif choice == "4":
            show_configuration_options()
        elif choice == "5":
            print("再见！")
            break
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
