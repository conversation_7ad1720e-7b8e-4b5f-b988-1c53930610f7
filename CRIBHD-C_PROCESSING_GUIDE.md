# CRIBHD-C 模拟危机场景处理指南

## 概述

CRIBHD-C是CribHD数据集中的特殊子集，包含120张使用仿真玩偶和道具构建的模拟危机场景图像。与CRIBHD-T和CRIBHD-B不同，CRIBHD-C专门用于训练危机识别能力。

## 数据集特点

### 基本信息
- **图像数量**: 120张
- **场景类型**: 模拟危机场景
- **风险等级**: 高风险
- **构建方式**: 仿真玩偶 + 多种道具
- **主要用途**: 危机识别训练

### 包含的风险场景
- 面部/四肢被遮挡
- 玩具靠近口鼻
- 窒息风险模拟
- 活动受限场景
- 其他安全隐患

## 数据结构差异

### CRIBHD-T/B (标准结构)
```
CRIBHD-T/
├── COCO/
│   ├── train/
│   │   ├── images/
│   │   └── _annotations.coco.json
│   ├── test/
│   └── valid/
```

### CRIBHD-C (特殊结构)
```
CRIBHD-C/
├── images/          # 主要图像目录
├── data/           # 可能的备选目录
├── crisis_scenes/  # 可能的备选目录
├── annotations.json     # 可能的注释文件
└── metadata.json       # 可能的元数据文件
```

## 特殊处理功能

### 1. 智能路径检测
代码会自动检测多种可能的图像路径：
- `CRIBHD-C/images/`
- `CRIBHD-C/` (直接在根目录)
- `CRIBHD-C/data/`
- `CRIBHD-C/crisis_scenes/`

### 2. 多格式支持
支持多种图像格式：
- `.jpg`, `.jpeg`, `.png`
- `.JPG`, `.JPEG`, `.PNG`

### 3. 特殊注释处理
为CRIBHD-C图像生成特殊的注释信息：
```python
{
    "dataset_type": "CRIBHD-C",
    "scene_type": "simulated_crisis",
    "risk_level": "high",
    "special_instructions": {
        "focus_areas": [
            "识别面部是否被遮挡",
            "检查四肢活动是否受限",
            "评估玩具与口鼻的距离",
            "分析整体安全风险"
        ]
    }
}
```

### 4. 专门的提示词
为CRIBHD-C图像使用特殊的分析提示词：
- 强调这是模拟危机场景
- 重点关注安全风险识别
- 提供针对性的预防建议
- 评估真实场景的风险等级

## 使用方法

### 1. 测试CRIBHD-C处理
```bash
python test_cribhd_c_processing.py
```

### 2. 单独处理CRIBHD-C
```bash
python cribnet_to_text_converter.py \
    --dataset_path ./cribhd \
    --output_path ./output/cribhd_c_results \
    --subset cribhd_c \
    --model_type qwen \
    --use_llm_guidance
```

### 3. 在代码中使用
```python
from cribnet_to_text_converter import CribNetTextConverter

converter = CribNetTextConverter(
    model_type="qwen",
    api_key="your_api_key",
    use_llm_guidance=True
)

# 专门处理CRIBHD-C
converter.convert_dataset(
    dataset_path="./cribhd",
    output_path="./output",
    subset="cribhd_c",
    max_images=10  # 测试用
)
```

## 输出格式

CRIBHD-C的处理结果包含特殊字段：

```json
{
    "image_id": "crisis_001",
    "subset_type": "crisis_scene",
    "scene_category": "simulated_crisis",
    "hazard_category": "high_risk",
    "expected_risks": [
        "面部遮挡",
        "四肢被困", 
        "玩具靠近口鼻",
        "窒息风险"
    ],
    "dataset_notes": "使用仿真玩偶和道具构建的模拟危机场景",
    "risk_assessment": {
        "risk_level": "高风险",
        "specific_risks": ["窒息危险", "活动受限"],
        "safety_recommendations": [
            "立即移除遮挡物",
            "确保呼吸道通畅",
            "检查四肢活动自由度"
        ]
    }
}
```

## 注意事项

### 1. 数据验证
- 预期120张图像，如果数量不符会给出警告
- 自动检测实际的图像路径
- 支持有无注释文件的情况

### 2. 风险识别重点
- 面部遮挡检测
- 呼吸道阻塞风险
- 四肢活动受限
- 小物品窒息风险

### 3. 处理建议
- 建议使用LLM增强模式获得更好的危机识别效果
- 可以先用少量图像测试处理效果
- 注意API调用成本控制

## 故障排除

### 问题1: 找不到图像文件
**解决方案**: 
- 检查CRIBHD-C目录结构
- 确认图像文件格式
- 查看控制台输出的路径检测信息

### 问题2: 图像数量不是120张
**解决方案**:
- 确认数据集下载完整
- 检查是否有重复文件
- 验证文件格式是否正确

### 问题3: 处理结果风险识别不准确
**解决方案**:
- 使用LLM增强模式
- 检查提示词是否正确加载
- 验证注释信息是否完整

## 技术实现

### 关键方法
- `_load_cribhd_c_format()`: 专门加载CRIBHD-C数据
- `_prepare_cribhd_c_annotations()`: 准备特殊注释
- `_convert_subset()`: 处理危机场景转换

### 特殊处理逻辑
1. 多路径智能检测
2. 格式兼容性处理
3. 危机场景特殊标注
4. 高风险提示词生成
5. 安全评估增强

这个处理方案确保CRIBHD-C的120张模拟危机场景图像能够得到专业、准确的分析和处理。
